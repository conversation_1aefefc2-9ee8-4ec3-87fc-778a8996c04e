import { jwtDecode, type JwtPayload } from "jwt-decode"
import AppError from "./AppError"
import Storage from "./Storage";
import { AuthState, User } from "~/models";

interface JwtPayloadType extends JwtPayload {
  role: string;
  permissions: string[];
}

export default class Token {
  static validate(accessToken?: string, remember = false, throws = false): AuthState {
    accessToken ||= Storage.readToken()
    try {
      const payload: JwtPayloadType = jwtDecode(accessToken!)
      if (Token.isValide(payload.exp)) {
        Storage.persistToken(accessToken, remember)
        const user = {
          email: payload.sub,
          role: payload.role,
          permissions: payload.permissions
        } as User
        return {
          isAuthenticated: true,
          token: accessToken,
          user
        }
      } else if (throws) throw new AppError("Token Expired")
    } catch (error: any) {
      if (throws) throw new AppError(error.message || "Token Error", error)
    }
    // pour tous les autres cas
    return { isAuthenticated: false, token: "" }
  }

  private static isValide(exp: number = 0): boolean {
    return (exp - Date.now() / 1000) > 0
  }
}
