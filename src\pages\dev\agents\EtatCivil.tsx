import {
  ProFormDatePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from "@ant-design/pro-components"
import { Image } from "antd"
import { getBase64WithCallback } from "src/services/utils"
// import accounts from "assets/imgs/account.svg"

interface EtatCivilProps {
  imageUrl: string | null;
  onImageChange: (url: string) => void;
}

function EtatCivil({ imageUrl, onImageChange }: EtatCivilProps) {
  const handleChange = file => {
    // alert(info.file.status);
    // if (info.file.status === "error") {
    //   // Get this url from response in real world.
    // }

    getBase64WithCallback(file, url => {
      onImageChange(url)
    })

    return false
  }
  return (
    <>
      {/* <ProFormGroup> */}
      <div className="flex flex-row-reverse w-full ">
        <ProFormGroup
          direction="vertical"
          title="Image"
          colProps={{
            span: 9,
          }}
        >
          <Image width={200} height={200} src={imageUrl} />
          <ProFormUploadButton
            // className="w-full m-1"
            width={"100%"}
            title="Upload"
            name="upload"
            // label="Image"
            fieldProps={{
              showUploadList: false,
              beforeUpload: f => handleChange(f),
            }}
            // onChange={handleChange}
          />
        </ProFormGroup>

        <ProFormGroup
          direction="vertical"
          colProps={{
            span: 15,
          }}
        >
          <ProFormSelect
            width="md"
            // fieldProps={{
            //   labelInValue: true,
            // }}
            request={async () => [
              { label: "Catégorie A", value: "A" },
              { label: "Catégorie B", value: "B" },
              { label: "Catégorie C", value: "C" },
              { label: "Catégorie D", value: "D" },
            ]}
            name="categorie"
            label="Catégorie"
          />
          <ProFormText width="md" name="nom" label="Nom" />
          <ProFormText width="md" name="prenom" label="Prénom" />
          <ProFormText width="md" name="email" label="Email" />
        </ProFormGroup>
      </div>
      {/* </ProFormGroup> */}

      <ProFormGroup direction="horizontal">
        <ProFormSelect
          colProps={{
            span: 8,
          }}
          name="sexe"
          label="Sexe"
          valueEnum={{
            M: "Masculin",
            F: "Féminin",
          }}
          // placeholder="Please select a country"
          // rules={[{ required: true, message: 'Please select your country!' }]}
        />
        <ProFormDatePicker
          width="100%"
          colProps={{
            span: 8,
          }}
          name="dateNais"
          label="Date de naissance"
        />
        <ProFormText
          colProps={{
            span: 8,
          }}
          name="lieuNais"
          label="Lieu de naissance"
        />
        <ProFormSelect
          colProps={{
            span: 6,
          }}
          name="situationFam"
          label="Situation Familiale"
          valueEnum={{
            C: "Célibataire",
            M: "Marié",
          }}
          // placeholder="Please select a country"
          // rules={[{ required: true, message: 'Please select your country!' }]}
        />
        <ProFormDatePicker
          width="100%"
          colProps={{
            span: 6,
          }}
          name="dateMariage"
          label="Date de mariage"
        />
        <ProFormDigit
          colProps={{
            span: 6,
          }}
          label="Enfants à charge"
          name="enfantCharge"
          min={0}
          // max={10}
        />
        <ProFormDigit
          colProps={{
            span: 6,
          }}
          label="Nombre enfants"
          name="nbrEnfant"
          min={0}
          // max={10}
        />

        <ProFormText
          colProps={{
            span: 6,
          }}
          name="cell"
          label="Cellulaire"
        />
        <ProFormText
          colProps={{
            span: 6,
          }}
          name="addr1"
          label="Adresse1"
        />
        <ProFormText
          colProps={{
            span: 6,
          }}
          name="addr2"
          label="Adresse2"
        />
        <ProFormText
          colProps={{
            span: 6,
          }}
          name="bp"
          label="BP"
        />
        <ProFormTextArea name="observation" label="Observation" />
      </ProFormGroup>
    </>
  )
}

export default EtatCivil
