import { v7 as uuidv7, v6 as uuidv6, v4 as uuidv4 } from "uuid"
export { themeIcon } from "./themeIcon"

export const waitTime = (time: number = 100): Promise<boolean> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export function getBase64(file: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

export function getBase64WithCallback(img: Blob, callback: { (url: any): void; (arg0: string | ArrayBuffer | null): any }) {
  const reader = new FileReader()
  reader.addEventListener("load", () => callback(reader.result))
  reader.readAsDataURL(img)
}

export const toggleTheme = (theme: any): any => {
  switch (theme) {
    case "auto":
      return "dark"
    case "dark":
      return "light"
    default:
      return "auto"
  }
}

export const device = /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent) ? "MOBILE" : "DESKTOP"

export const toPath = (str: string): string => (!str ? import.meta.env.BASE_URL : `${str.startsWith("/") ? '':'/'}${str}${str.endsWith("/") ? '':'/'}`)

export function createPath(sublink: string, root: string = "/") {
  sublink = sublink.startsWith("/") ? sublink.substring(1) : sublink;
  sublink = sublink.endsWith("/") ? sublink.substring(0, sublink.length - 1) : sublink;
  root = root.startsWith("/") ? root : `/${root}`;
  root = root.endsWith("/") ? root : `${root}/`;
  return `${root}${sublink}`
}

export const getUUID = (name?: string | null, nbrVersion: number = 4): string => {
  const loadUuidBy = (nbr?: number) => {
    switch (nbr) {
      case 7:
        return uuidv7()
      case 6:
        return uuidv6()
      default:
        return uuidv4()
    }
  }
  return name ? `${name}%${loadUuidBy(nbrVersion)}` : loadUuidBy(nbrVersion)
}
