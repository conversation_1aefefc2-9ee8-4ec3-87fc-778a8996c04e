import {
  LockOutlined,
  LoginOutlined,
  LogoutOutlined
} from "@ant-design/icons"
import { Button, Col, Flex, FloatButton, Layout, Row, Tooltip, Typography } from "antd"
import { useResponsive } from "antd-style"
import { useEffect, useRef, useState } from "react"
// import { Link } from "react-router"
import { AppLogo } from "~/components"
import { PATH_ADMIN, PATH_AUTH, PATH_GUEST, PATH_LINKS } from "~/constants"
import { useAppAuth } from "~/hooks"
import { createStyles } from "~/themes"

const { Header, Content, Footer } = Layout

const { Title, Text, Paragraph, Link } = Typography

export const Landing = ({children}: {children: React.ReactNode}) => {
  // const theme = useTheme()
  const { laptop, mobile } = useResponsive()
  // const location = useLocation()
  const nodeRef = useRef(null)
  const [navFill, setNavFill] = useState(false)
  // const [open, setOpen] = useState(false)
  const isMobile = !!mobile
  const isLaptop = !laptop
  const { styles } = useStyles({ isMobile, navFill })
  const { isAuthenticated, logout } = useAppAuth();

  // const showDrawer = () => {
  //   setOpen(true)
  // }

  // const onClose = () => {
  //   setOpen(false)
  // }

  useEffect(() => {
    const updateNavFill = () => {
      if (window.scrollY > 50) {
        setNavFill(true)
      } else {
        setNavFill(false)
      }
    }
    window.addEventListener("scroll", updateNavFill)

    return () => window.removeEventListener("scroll", updateNavFill)
  }, [])

  return (
    <>
      <Layout className="layout min-h-screen">
        <Header className={`${styles.clHeader1} bg-[#09335d]`}>
          <AppLogo color="blue" noTexte={isLaptop} asLink href={PATH_GUEST.root} />
          {!isMobile ? (
            <Flex gap="middle">
              {isAuthenticated ? (
                <>
                  <Link href={PATH_ADMIN.root}>
                    <Button icon={<LockOutlined />} type="primary">
                      Espace Privé
                    </Button>
                  </Link>
                  {/* <a href={PATH_ADMIN.root}>
                    <Button icon={<LockOutlined />} type="primary">
                      Espace Privé
                    </Button>
                  </a> */}
                  <Link href="#" onClick={logout}>
                    <Button icon={<LogoutOutlined />} type="primary" danger>
                      Déconnexion
                    </Button>
                  </Link>
                </>
              ) : (
                <Link href={PATH_AUTH.signin}>
                  <Button icon={<LoginOutlined />} type="primary">
                    Connexion
                  </Button>
                </Link>
              )}
            </Flex>
          ) : (
            <Flex gap="middle">
              {isAuthenticated ? (
                <>
                  <Tooltip title="Espace Privé">
                    <Link href={PATH_ADMIN.root}>
                      <Button icon={<LockOutlined />} type="primary" size="middle" />
                    </Link>
                  </Tooltip>
                  <Tooltip title="Déconnexion">
                    <Link href="#" onClick={logout}>
                      <Button icon={<LogoutOutlined />} type="primary" danger size="middle" />
                    </Link>
                  </Tooltip>
                </>
              ) : (
                <Tooltip title="Connexion">
                  <Link href={PATH_AUTH.signin}>
                    <Button icon={<LoginOutlined />} type="primary" size="middle" />
                  </Link>
                </Tooltip>
              )}
            </Flex>
          )}
        </Header>
        <Content
          // className={cx(isMobile ? "pb-10" : "pb-20")}
          style={{
            transition: "all .25s",
          }}
        >
          <div ref={nodeRef} className="site-layout-content">
            {children}
          </div>
          <FloatButton.BackTop />
        </Content>
        <Footer className="p-0">
          <Row>
            <Col span={8} className="bg-green-700 min-h-3"></Col>
            <Col span={8} className="bg-yellow-400 min-h-3"></Col>
            <Col span={8} className="bg-red-600 min-h-3"></Col>
          </Row>
          <Row className={`${styles.clFooter} bg-[#0A3764]`}>
            <Col md={8} className="p-5">
              <Title level={3}>Liens utiles</Title>
              <Paragraph>
                <ul>
                  <li>
                    <Link href={PATH_LINKS.gouv} target="_blank">
                      Gouvernement Béninois
                    </Link>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.finance} target="_blank">
                      Ministère des Finances
                    </Link>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.omd} target="_blank">
                      Organisation Mondiale des Douanes
                    </Link>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.omc} target="_blank">
                      Organisation Mondiale du Commerce
                    </Link>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.ecowas} target="_blank">
                      UEMOA || CEDEAO
                    </Link>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.bc} target="_blank">
                      Benin Control
                    </Link>
                  </li>
                </ul>
              </Paragraph>
            </Col>
            <Col md={8} className="p-5">
              <Title level={3}>Les recettes principales</Title>
              <Paragraph>
                <ul>
                  <li>
                    <Text>Recette des douanes de Cotonou Port</Text>
                  </li>
                  <li>
                    <Text>Recette des douanes de Cotonou Aéroport</Text>
                  </li>
                  <li>
                    <Text>Recette des douanes de Cotonou Hydrocarbures</Text>
                  </li>
                  <li>
                    <Text>Recette des douanes d'Hilla Condji</Text>
                  </li>
                  <li>
                    <Text>Recette des douanes de Krake-Plage</Text>
                  </li>
                </ul>
              </Paragraph>
            </Col>
            <Col md={8} className="p-5">
              <Title level={3}>Adresse</Title>
              <Paragraph>
                <ul>
                  <li>
                    <Text>
                      <b>Adresse:</b> 01 BP : 400, Cotonou Avenue Jean-Paul II – Port de Cotonou.
                    </Text>
                  </li>
                  <li>
                    <Text>
                      <b>Téléphone:</b> (+229) 21 31 50 54 / 21 31 50 55
                    </Text>
                  </li>
                  <li>
                    <Text>
                      <b>Numéro vert:</b> (+229) 91 13 13 13
                    </Text>
                  </li>
                  <li>
                    <Text>
                      <b>Mail:</b> <EMAIL>
                    </Text>
                  </li>
                  <li>
                    <Link href={PATH_LINKS.map} target="_blank" className="font-bold">
                      Voir sur une carte
                    </Link>
                  </li>
                </ul>
              </Paragraph>
            </Col>
          </Row>
          <Row className="p-4 text-white bg-[#09335d]">
            <Col span={24} className="text-center">
              &copy; {import.meta.env.VITE_APP_AUTHOR} 2025 Tous droits réservés.
            </Col>
          </Row>
        </Footer>
      </Layout>
      {/* <Drawer title="Menu" placement="top" onClose={onClose} open={open}>
        <Flex gap="small" vertical>
          <Link href={"#"} target="_blank">
            <Button icon={<HomeTwoTone />} type="link" className="text-black font-semibold">
              Accueil
            </Button>
          </Link>
          <Divider />
          <Link href={PATH_LINKS.customsWebb} target="_blank">
            <Button icon={<ProductOutlined />} type="link" className="text-black font-semibold">
              Customs Webb
            </Button>
          </Link>
          <Link href={PATH_LINKS.guce} target="_blank">
            <Button icon={<AppstoreAddOutlined />} type="link" className="text-black font-semibold">
              Guce Bénin
            </Button>
          </Link>
        </Flex>
      </Drawer> */}
    </>
  )
}

const useStyles = createStyles(({ css }, opt: { isMobile: boolean; navFill: boolean }) => {
  return {
    clHeader1: {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      // background: navFill ? 'rgba(255, 255, 255, .5)' : 'none',
      backdropFilter: opt.navFill ? "blur(8px)" : "none",
      boxShadow: opt.navFill ? "0 0 8px 2px rgba(0, 0, 0, 0.05)" : "none",
      gap: 12,
      position: "sticky",
      top: 0,
      padding: opt.isMobile ? "0 1rem" : "0 2rem",
      zIndex: 9999,
      height: "var(--dgd-head-height)",
    },
    clHeaderMenu: css`
      color: white;
    `,
    clFooter: css`
      padding: 0.5rem;
      & * {
        color: white !important;
      }
      & h3 {
        text-transform: uppercase;
      }
      & a:hover {
        color: #e5e7eb !important;
      }
      & ul {
        list-style-type: none;
      }
      & ul li {
        margin: 10px;
      }
    `,
  }
})
