import { Theme, ThemeMode } from "antd-style"
import { useCallback, useEffect, useMemo, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
import { updateAppStore } from "~/redux/slices/appSlice"
import { RootState } from "~/redux/store"
import { toggleTheme } from "~/services/utils"
import { useTheme } from "~/themes"

export interface AppTheme extends Theme {
  updateTheme: (theme?: any) => void
  theme: ThemeMode
}

const useAppTheme = (): AppTheme => {
  const dispatch = useDispatch()
  const { theme: appTheme } = useSelector((state: RootState) => state.app)
  const allTheme = useTheme()
  const ref = useRef({ appTheme, allTheme, dispatch })

  const handleChangeTheme = useCallback((theme?: any) => {
    if (!theme) theme = toggleTheme(appTheme)
    ref.current.allTheme.setThemeMode(theme)
    ref.current.dispatch(updateAppStore({ theme }))
  }, [appTheme])

  useEffect(() => {
    const theme = ref.current.appTheme
    ref.current.allTheme.setThemeMode(theme)
    ref.current.dispatch(updateAppStore({ theme }))
  }, [])

  return useMemo(
    () => ({ theme: appTheme, updateTheme: handleChangeTheme, ...ref.current.allTheme }),
    [handleChangeTheme, appTheme]
  )
}

export default useAppTheme
