import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { AuthState, LoginPayload } from '~/models';
import Storage from '~/services/Storage';
import Token from '~/services/Token';
import { apiLogin } from "~/services/api";

const init = { token: "", isAuthenticated: false }

const initialState: AuthState = {
  ...init,
  ...Token.validate(),
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    updateAuthStore(state: AuthState, action) {
      Object.assign(state, action.payload)
    },
  },
  extraReducers: (builder) => {
    builder.addCase(doLogin.fulfilled, (state, action) => {
      if ('isAuthenticated' in action.payload && action.payload.isAuthenticated) {
        Object.assign(state, action.payload);
      }
    })
  }
});
export default authSlice;

export const { updateAuthStore } = authSlice.actions;

export const doLogin = createAsyncThunk<AuthState | {message:string}, LoginPayload>("auth/doLogin", async (loginPayload) => {
  try {
    const response = await apiLogin(loginPayload);
    if (import.meta.env.DEV) console.log("apiLogin response :", response)

    return Token.validate(response.data.token, loginPayload.remember, true);
  } catch (error:any) {
    if (import.meta.env.DEV) console.dir(error, { depth: null })
    return { message: error.message }
  }
})

export const doLogout = createAsyncThunk<void, void>("auth/doLogout", async (_, { dispatch }) => {
  Storage.deleteToken()
  dispatch(updateAuthStore(init))
})

// export const doSignUp = createAsyncThunk("auth/doSignUp", async signUpPayload => apiRegister(signUpPayload))

// export const doResetPassword = createAsyncThunk(
//   'auth/doResetPassword',
//   async (resetPassPayload) => resetPassword(resetPassPayload),
// );

// export const doVerifySecurityCode = createAsyncThunk(
//   'auth/doVerifySecurityCode',
//   async (securityCodePayload) => verifySecurityCode(securityCodePayload),
// );

// export const doSetNewPassword = createAsyncThunk('auth/doSetNewPassword', async (newPasswordData) =>
//   setNewPassword(newPasswordData),
// );

