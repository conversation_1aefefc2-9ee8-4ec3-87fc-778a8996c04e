import { red } from "@ant-design/colors"
import { CloseCircleOutlined } from "@ant-design/icons"
import { Button, Result, Typography } from "antd"
import { useCallback, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams, useRouteError } from "react-router"

const { Paragraph, Text } = Typography

const ErrorPage = ({ code, message }: { code?: number, message?: string }) => {
  const rootError:any = useRouteError()
  const navigate = useNavigate()
  const { t } = useTranslation()
  const {id} = useParams()

  const getMessage= useCallback(()=>{
    let errorMessage = message
    if (!rootError) {
      if(!errorMessage) {
        const newCode = id ?? code?.toString()

        switch (newCode) {
          case "400":
            errorMessage =
              "Bad request. The request could not be understood by the server due to malformed syntax."
            break
          case "403":
            errorMessage = "Sorry, you are not authorized to access this page."
            break
          case "404":
            errorMessage = "Sorry, the page you visited does not exist."
            break
          case "500":
          case "503":
            errorMessage = "Sorry, something went wrong."
            break

          default:
            errorMessage = "An error occurred."
            break
        }
      }
    } else errorMessage = rootError.response?.statusText || rootError.message || "Unknown error"

    return errorMessage
  },[rootError, id, code, message])

  const getStatus= useCallback(()=>{
    const newCode = rootError?.status || (id ?? code?.toString())
    return ["404","403","500","error", "success", "info", "warning"].find(el => el == newCode)
  },[rootError, id, code])

  useEffect(()=>{
    if (import.meta.env.DEV) {
      console.error("=====> ERROR <=====\n")
      console.log('id', id)
      console.log('code', code)
      console.log('message', message)
      console.dir(rootError)
      console.log("=====> END ERROR <=====\n")
    }
  },[rootError,id,code,message])


  return (
    <Result
      status={getStatus() ?? "error" as any}
      title={`${getStatus() ?? "Oops!"}`}
      subTitle={getMessage()}
      extra={[
        <Button key="back" onClick={() => navigate(-1)}>
          {t("GoBack")}
        </Button>,
        <Button key="home" type="primary" onClick={() => navigate("/")}>
          {t("BackHome")}
        </Button>,
      ]}
    >
      {import.meta.env.PROD ? null : (
        <div className="desc">
          <Paragraph>
            <Text
              strong
              style={{
                fontSize: 16,
              }}
            >
              The content you submitted has the following error:
            </Text>
          </Paragraph>

          <Paragraph copyable>
            <CloseCircleOutlined style={{ color: red[5] }} />
            {getMessage()}
          </Paragraph>
        </div>
      )}
    </Result>
  )
}

export default ErrorPage
