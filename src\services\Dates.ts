import dayjs, { type Dayjs } from "dayjs"
import LocalizedFormat from "dayjs/plugin/localizedFormat"
import localeData from "dayjs/plugin/localeData"
import isBetween from "dayjs/plugin/isBetween"
import "dayjs/locale/fr"
import "dayjs/locale/en"

dayjs.extend(LocalizedFormat)
dayjs.extend(localeData)
dayjs.extend(isBetween)

export default class Dates {
  static setLocale(locale:string) {
    dayjs.locale(locale)
  }

  static getToday(): Dayjs {
    return dayjs()
  }

  static getClearDate(): Dayjs {
    return this.getToday().hour(0).minute(0).second(0).millisecond(0)
  }

  static getMonths(): string[] {
    return dayjs.months()
  }

  static getDays(): string[] {
    return dayjs.weekdaysShort()
  }

  static getDate(date: string | number): Dayjs {
    return dayjs(date)
  }

  static formatDate(date: Dayjs | string | number, query = "DD/MM/YYYY") {
    if (typeof date === "string" || typeof date === "number") {
      return dayjs(date).format(query)
    } else {
      return date.format(query)
    }
  }
}
