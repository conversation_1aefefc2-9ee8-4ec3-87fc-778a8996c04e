import { createSlice } from "@reduxjs/toolkit"
import Storage from "~/services/Storage"
import { Local, ThemeMode } from "~/models"

export interface AppState {
  local: Local
  theme: ThemeMode
}

const initialState: AppState = {
  local: Storage.readLocal(),
  theme: Storage.readTheme(),
}

const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    updateAppStore(state: AppState, action) {
      Object.assign(state, action.payload)
    },
  },
  // extraReducers: (builder) => {
  //   builder.addCase(setLocalStore, (state, action) => {
  //     Object.assign(state, { locale: action.payload })
  //     Storage.persistLocal(state.local)
  //   })
  // }
})
export default appSlice

export const { updateAppStore } = appSlice.actions

// export const setLocalStore = createAction("locale/setLocale", (newLocal = "fr") => {
//   return {
//     payload: newLocal,
//   }
// })

// Make sure to update storage for first time
Storage.persist(initialState, true)
