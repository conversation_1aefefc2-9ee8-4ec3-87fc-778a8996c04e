{"semi": false, "printWidth": 120, "endOfLine": "auto", "arrowParens": "avoid", "experimentalTernaries": true, "trailingComma": "es5", "bracketSpacing": true, "jsxBracketSameLine": false, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "proseWrap": "preserve", "plugins": ["prettier-plugin-tailwindcss", "prettier-plugin-organize-imports", "prettier-plugin-package<PERSON><PERSON>"], "tailwindConfig": "./tailwind.config.ts", "importOrderSeparation": true, "importOrderSortSpecifiers": true}