import agents from '../../mocks/fixtures/agents';
import { Agent, AgentListResponse, AgentRepartition } from '../../models/Agent';
import request from '../request';

const URL = '/agents';

export const getAgents = async (): Promise<AgentListResponse> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    const response = await request.get(URL);
    return Promise.resolve(response);

    // Pour le moment, on utilise les données mockées
    // return Promise.resolve({
    //   data: agents,
    //   total: agents.length
    // });
  } catch (error) {
    console.error('Error fetching agents:', error);
    throw error;
  }
};

export const getAgentById = async (id: string): Promise<Agent> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.get(`${API_URL}/${id}`);
    // return response.data;

    // Pour le moment, on utilise les données mockées
    const agent = agents.find(a => a.id === id);
    if (!agent) {
      throw new Error(`Agent with id ${id} not found`);
    }
    return Promise.resolve(agent);
  } catch (error) {
    console.error(`Error fetching agent with id ${id}:`, error);
    throw error;
  }
};

export const saveAgentRepartitions = async (repartitions: AgentRepartition[]): Promise<void> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // await request.post(`${API_URL}/repartitions`, { data: repartitions });

    // Pour le moment, on simule juste un délai
    await new Promise(resolve => setTimeout(resolve, 500));
    return Promise.resolve();
  } catch (error) {
    console.error('Error saving agent repartitions:', error);
    throw error;
  }
};

export const updateAgent = async (id: string, agentData: Partial<Agent>): Promise<Agent> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.put(`${API_URL}/${id}`, { data: agentData });
    // return response.data;

    // Pour le moment, on simule juste un délai et on retourne l'agent modifié
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simuler la mise à jour de l'agent dans les données mockées
    const agent = agents.find(a => a.id === id);
    if (!agent) {
      throw new Error(`Agent with id ${id} not found`);
    }

    const updatedAgent = { ...agent, ...agentData };
    const index = agents.findIndex(a => a.id === id);
    if (index !== -1) {
      agents[index] = updatedAgent;
    }

    return Promise.resolve(updatedAgent);
  } catch (error) {
    console.error(`Error updating agent with id ${id}:`, error);
    throw error;
  }
};
