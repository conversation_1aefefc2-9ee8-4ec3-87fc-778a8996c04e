import * as XLSX from 'xlsx';
import { Prime, PrimeStatus, PrimeType } from '../../models/Prime';

/**
 * Parse an Excel file and convert it to Prime objects
 */
export const parseExcelToPrimes = async (file: File, type: string): Promise<Prime[]> => {
  try {
    // Read the Excel file
    const data = await readExcelFile(file);
    
    if (!data || !data.length) {
      throw new Error('Le fichier Excel est vide ou mal formaté');
    }
    
    // Map Excel rows to Prime objects
    const primes = data.map((row: any, index: number) => {
      // Validate required fields
      if (!row.name || !row.amount || !row.date) {
        throw new Error(`Ligne ${index + 2}: Des champs obligatoires sont manquants (nom, montant ou date)`);
      }

      // Create a new Prime object
      const prime: Prime = {
        id: `temp-${index}`, // Temporary ID, will be replaced by the server
        name: row.name,
        type: type as PrimeType,
        amount: parseFloat(row.amount),
        date: formatDate(row.date),
        status: PrimeStatus.DRAFT,
        fileName: file.name
      };

      return prime;
    });

    return primes;
  } catch (error) {
    console.error('Error parsing Excel file:', error);
    throw error;
  }
};

/**
 * Read an Excel file and return the data as an array of objects
 */
const readExcelFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error('Error reading file'));
          return;
        }
        
        const workbook = XLSX.read(data, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON with headers
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = (error) => reject(error);
    
    // Read the file as binary
    reader.readAsBinaryString(file);
  });
};

/**
 * Format a date from Excel to a string format
 */
const formatDate = (excelDate: any): string => {
  // If already a string in date format, return it
  if (typeof excelDate === 'string' && !isNaN(Date.parse(excelDate))) {
    return new Date(excelDate).toISOString();
  }
  
  // If a number (Excel date format), convert it
  if (typeof excelDate === 'number') {
    // Excel dates are number of days since 1900-01-01
    const date = new Date(Math.round((excelDate - 25569) * 86400 * 1000));
    return date.toISOString();
  }
  
  // Default to current date if invalid
  return new Date().toISOString();
}; 