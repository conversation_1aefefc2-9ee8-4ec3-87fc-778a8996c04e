import React from 'react';
import { PageContainer, PageContainerProps } from '@ant-design/pro-components';
import { Tooltip } from 'antd';
import { createStyles } from 'antd-style';
import { LeftOutlined } from '@ant-design/icons';

// Styles personnalisés pour AppContainer
const useStyles = createStyles(({ token, css }) => ({
  appContainer: css`
    min-height: calc(100vh - 64px);
    background: linear-gradient(135deg, ${token.colorBgLayout} 0%, ${token.colorBgContainer} 100%);

    .dgd-page-header {
      margin-bottom: ${token.marginSM}px;
    }

    &.app-container-glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);

      .dgd-page-header {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
      }
    }

    &.app-container-gradient {
      background: linear-gradient(135deg,
        ${token.colorPrimary}08 0%,
        ${token.colorSuccess}08 50%,
        ${token.colorInfo}08 100%);
    }
  `,
}));

export interface AppContainerProps extends Omit<PageContainerProps, 'title'> {
  /** Icône pour le titre */
  icon?: React.ReactNode;
  /** Titre de la page */
  title?: React.ReactNode;
  /** Variante de style */
  variant?: 'default' | 'glass' | 'gradient';
  /** Texte du bouton retour */
  backButtonText?: string;
}

const AppContainer: React.FC<AppContainerProps> = ({
  icon,
  title,
  variant = 'gradient',
  backButtonText = 'Retour',
  className,
  children,
  ...props
}) => {
  const { styles, cx } = useStyles();

  // Classes CSS
  const containerClasses = cx(
    styles.appContainer,
    {
      'app-container-glass': variant === 'glass',
      'app-container-gradient': variant === 'gradient',
    },
    className
  );

  return (
    <PageContainer
      {...props}
      className={containerClasses}
      title={<div>{icon && <span className='mr-2'>{icon}</span>}{title}</div>}
      backIcon={<Tooltip title={backButtonText}><LeftOutlined /></Tooltip>}
    >
      {children}
    </PageContainer>
  );
};

export default AppContainer;
