// color palettes: triadic #A1A7CB, #CBA1A7, #A7CBA1
// 10 color objects of primary #2378c3 as generated by https://smart-swatch.netlify.app/#2378c3
// This is for reference purposes

export const COLOR = {
  50: "#e0f1ff",
  100: "#b0d2ff",
  200: "#7fb0ff",
  300: "#4d8bff",
  400: "#1e79fe",
  500: "#076ee5",
  600: "#0062b3",
  700: "#004f81",
  800: "#003650",
  900: "#001620",
  borderColor: "#E7EAF3B2",
}

const BASE_COLORS = {
  white: '#ffffff',
  black: '#000000',
  green: '#008000',
  orange: '#ffb155',
  gray: '#808080',
  lightgray: '#c5d3e0',
  violet: '#ee82ee',
  lightgreen: '#89dca0',
  pink: '#ffc0cb',
  blue: '#0000ff',
  skyblue: '#35a0dc',
  red: '#ff5252',
};

const bothTheme = {
  ...BASE_COLORS,
  borderRadius: 50,
  borderColor: '#E7EAF3B2',
}

const lightTheme = {
  primary: '#076ee5',
  secondary: '#0085FF',
  background: '#ecf3ff',
  success: '#30AF5B',
  warning: '#FFB155',
  error: '#FF5252',
  ...bothTheme,
  palette: COLOR,
}

const darkTheme = {
  primary: "#267FC4",
  secondary: '#0072DD',
  background: '#25284B',
  success: '#57D682',
  warning: '#FFB765',
  error: '#FF5252',
  ...bothTheme,
  palette: COLOR,
}

export const THEMES: any = {
  light: lightTheme,
  dark: darkTheme,
}
