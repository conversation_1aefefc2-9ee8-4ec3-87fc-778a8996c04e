import { useCallback, useEffect, useMemo, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { Local } from "~/models"
import { updateAppStore } from "~/redux/slices/appSlice"
import { RootState } from "~/redux/store"
import Dates from "~/services/Dates"
import Storage from "~/services/Storage"

const useAppLocal = () => {
  const dispatch = useDispatch()
  const { i18n } = useTranslation()
  const { local: myLocal } = useSelector((state: RootState) => state.app)
  const ref = useRef({ dispatch, i18n, myLocal })

  const handleChangeLocal = useCallback(async (local: string) => {
    Dates.setLocale(local)
    await ref.current.i18n.changeLanguage(local)
    Storage.persistLocal(local as Local)
    ref.current.dispatch(updateAppStore({ local }))
  }, [])

  useEffect(() => {
    handleChangeLocal(ref.current.myLocal)
  }, [handleChangeLocal])

  return useMemo(() => ({ local: i18n.language, setLocal: handleChangeLocal }), [handleChangeLocal, i18n.language])
}

export default useAppLocal
