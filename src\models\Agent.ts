export interface Agent {
  id: string;
  matricule: string;
  nom: string;
  prenom: string;
  grade: string;
  fonction: string;
  unite: string;
  coefficient: number;
}

export interface AgentRepartition {
  agentId: string;
  primeId: string;
  coefficient: number;
  montant: number;
}

export interface AgentListResponse {
  data: Agent[];
  total: number;
}


export interface CoefficientConfig {
  fonction: string;
  primeType: string;
  coefficient: number;
}
