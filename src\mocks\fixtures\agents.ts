import { Agent } from '../../models/Agent';
import IdentityManager from '../../services/IdentityManager';

const agents: Agent[] = [
  {
    id: IdentityManager.fetchUUID(),
    matricule: 'A001',
    nom: 'DUPONT',
    prenom: '<PERSON>',
    grade: 'Contrôleur Principal',
    fonction: 'chef_service',
    unite: 'Bureau Central',
    coefficient: 1.5
  },
  {
    id: IdentityManager.fetchUUID(),
    matricule: 'A002',
    nom: 'MARTIN',
    prenom: 'Sophie',
    grade: 'Inspecteur',
    fonction: 'responsable_unite',
    unite: 'Bureau Central',
    coefficient: 1.2
  },
  {
    id: IdentityManager.fetchUUID(),
    matricule: 'A003',
    nom: 'DUBOIS',
    prenom: 'Pierre',
    grade: 'Agent',
    fonction: 'agent_terrain',
    unite: 'Bureau Frontière Nord',
    coefficient: 1.0
  },
  {
    id: IdentityManager.fetchUUID(),
    matricule: 'A004',
    nom: 'LEROY',
    prenom: '<PERSON>',
    grade: 'Contrôleur',
    fonction: 'verificateur',
    unite: 'Bureau Frontière Sud',
    coefficient: 1.1
  },
  {
    id: IdentityManager.fetchUUID(),
    matricule: 'A005',
    nom: 'MOREAU',
    prenom: 'Thomas',
    grade: 'Inspecteur Principal',
    fonction: 'directeur_adjoint',
    unite: 'Direction Générale',
    coefficient: 1.8
  }
];

export default agents;
