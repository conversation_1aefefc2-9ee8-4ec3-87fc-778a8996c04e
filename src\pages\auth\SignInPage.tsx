import { Button, Dropdown, Flex, Form, Image, notification, Space, Typography } from "antd"
import { DownOutlined, LockOutlined, UserOutlined } from "@ant-design/icons"
import { Link, Navigate, useLocation, useNavigate } from "react-router"
import { useEffect, useRef, useState } from "react"
import { ProCard, ProFormCheckbox, ProFormText } from "@ant-design/pro-components"
import { useAppAuth, useAppLocal, useAppTheme } from "~/hooks"
import EnUSSvg from "~/assets/en_US.svg?url"
import FrFRSvg from "~/assets/fr_FR.svg"
import { useTranslation } from "react-i18next"
import { AppLoading, LogoDouane } from "~/components"
import { themeIcon } from "~/services/utils"
import { LoginPayload } from "~/models"
import { PATH_ADMIN, PATH_AUTH, PATH_GUEST } from "~/constants"

const { Title } = Typography

type FieldType = LoginPayload

const initialValues: FieldType = {
  email: "",
  password: "",
  remember: false,
}

const SignInPage = () => {
  const { isAuthenticated } = useAppAuth()
  const { theme, updateTheme } = useAppTheme()
  const { local, setLocal } = useAppLocal()
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const { login } = useAppAuth()
  const navigate = useNavigate()
  const locate = useLocation()
  const ref = useRef({ navigate, from: locate.state?.from })

  const onFinish = async (values: FieldType) => {
    setLoading(true)
    const res = await login(values)
    if ('message' in res) {
      notification.error({
        message: t("Erreur de connexion"),
        description: t(res.message),
        duration: 5,
        placement: "top",
      })
    }
    setLoading(false)
  }

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    updateTheme(newTheme);
  }

  useEffect(() => {
    if (isAuthenticated) ref.current.navigate(ref.current.from ?? PATH_GUEST.root)
  }, [isAuthenticated])

  if (isAuthenticated) return <AppLoading />; //<Navigate to={PATH_GUEST.root} replace />;

  return (
    <ProCard layout="center" className="min-h-screen">
      <Flex vertical align="center" justify="center" className={"text-center my-12"}>
        <div className="flex justify-between w-full mb-2">
          <Button
            shape="round"
            color="default"
            variant="filled"
            icon={themeIcon(theme)}
            onClick={() => toggleTheme()}
          />
          <Dropdown
            menu={{
              onClick: (info: any) => setLocal(info.key),
              items: [
                {
                  key: "en",
                  icon: <Image preview={false} src={EnUSSvg} alt="en" />,
                  disabled: local === "en",
                  label: <span className="ml-1">{t("English")}</span>,
                },
                {
                  key: "fr",
                  icon: <Image preview={false} src={FrFRSvg} alt="fr" />,
                  disabled: local === "fr",
                  label: <span className="ml-1">{t("French")}</span>,
                },
              ],
            }}
          >
            <Button
              shape="round"
              color="default"
              variant="filled"
              className="font-bold"
              onClick={e => e.preventDefault()}
            >
              <Space>
                {local}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
        <LogoDouane />
        <Title level={3} className="my-5 uppercase">
          {t("Connexion")}
        </Title>
        <Form<FieldType>
          onFinish={onFinish}
          className="w-[300px]"
          initialValues={initialValues}
        >
          <ProFormText
            name="email"
            fieldProps={{
              size: "large",
              prefix: <UserOutlined className={"prefixIcon"} />,
            }}
            placeholder={"<EMAIL>"}
            rules={[
              {
                required: true,
                message: t("Required"),
              },
              {
                type: "email",
                message: t("Email non valide"),
              },
            ]}
          />
          <ProFormText.Password
            name="password"
            fieldProps={{
              size: "large",
              prefix: <LockOutlined className={"prefixIcon"} />,
            }}
            rules={[
              {
                required: true,
                message: t("Required"),
              },
            ]}
          />
          <ProFormCheckbox noStyle name="remember">
            {t("Se souvenir de moi")}
          </ProFormCheckbox>
          <Form.Item className="mt-4">
            <Button size="large" htmlType="submit" type="primary" className="w-full" loading={loading}>
              {t("Se connecter")}
            </Button>
          </Form.Item>
        </Form>
        <div className="mb-4">
          <p>{t("Vous avez oublié votre mot de passe ou vous n'avez pas de compte ?")}</p>
          <Link to={PATH_AUTH.request}>{t("Cliquez ici pour faire votre demande")}</Link>
        </div>

        <footer className="text-center font-bold">{t("global.footer")}</footer>
      </Flex>
    </ProCard>
  )
}

export default SignInPage
