import { createPath } from "~/services/utils"

export const HOME = "home"
export const LOGIN = "login"
export const SERVICES = "services"
export const AUTH = "auth"
export const SIGNIN = "signin"
export const REQUEST = "request"
export const UPDATE_PASSWORD = "update-password"
export const ADMIN = "admin"
export const DASHBOARD = "dashboard"
export const DAFL = "dafl"
export const AGENTS = "agents"
export const CONVOYAGE = "convoyage"
export const UTILISATEURS = "utilisateurs"
export const PRIMES = "primes"

export const PATH_GUEST = {
  root: createPath(HOME),
  login: createPath(LOGIN),
  services: createPath(SERVICES),
}

export const PATH_AUTH = {
  root: createPath(AUTH),
  signin: createPath(SIGNIN, AUTH),
  request: createPath(REQUEST, AUTH),
  updatePassword: createPath(UPDATE_PASSWORD, AUTH),
}

export const PATH_ADMIN = {
  root: createPath(ADMIN),
  dashbord: createPath(DASHBOARD, ADMIN),
  dafl: createPath(DAFL, ADMIN),
  agents: createPath(AGENTS, ADMIN),
  convoyage: createPath(CONVOYAGE, ADMIN),
  utilisateurs: createPath(UTILISATEURS, ADMIN),
}

export const PATH_LINKS = {
  customsWebb: "https://cw.douanes.bj/",
  guce: "https://guce.gouv.bj/",
  gouv: "https://www.gouv.bj/",
  finance: "https://finances.bj/",
  omd: "http://www.wcoomd.org/fr.aspx",
  omc: "https://www.wto.org/",
  ecowas: "https://www.ecowas.int/?lang=fr",
  bc: "https://www.benincontrol.com/",
  map: "https://goo.gl/maps/bCPg6bWbZ1KMWQoc8",
}

export const PATH_ERROR = {
  error403: "/error/403",
  error404: "/error/404",
  error500: "/error/500",
}
