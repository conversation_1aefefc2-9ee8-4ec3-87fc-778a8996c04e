import { createGlobalStyle } from "./configs";

export const GlobalStyles = createGlobalStyle`
  :root {
    --dgd-head-height: 64px;
  }

  .ant-tabs-editable > .ant-tabs-nav .ant-tabs-tab {
    transition: none;
  }

  /* Styles pour la balise main - margin et padding à zéro */
  main {
    margin: 0 !important;
    padding: 0 !important;
  }

  .dgd-menu-item-disabled {
    cursor: default !important;
  }

  .dgd-page-header-heading-title {
    text-transform: uppercase;
  }
`;
