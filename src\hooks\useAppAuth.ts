import { useCallback, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LoginPayload } from '~/models';
import { doLogin, doLogout } from '~/redux/slices/authSlice';
import { RootState, AppDispatch } from '~/redux/store';

export const useAppAuth = () => {
  const auth = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();
  const ref = useRef({dispatch});


  const login = useCallback(async (loginPayload: LoginPayload) => {
    try {
      return await ref.current.dispatch(doLogin(loginPayload)).unwrap();
    } catch (error: any) {
      if (import.meta.env.DEV) console.error('Login error:', error);
      return {message: 'Login error : ' + error.message};
    }
  }, []);

  const logout = useCallback(() => ref.current.dispatch(doLogout()).catch((error: any) => {
      if (import.meta.env.DEV) console.error('Logout error:', error);
    }),
  []);

  const hasRole = useCallback((role: string) => {
    return auth.user?.role.toLowerCase() === 'admin' ? true : auth.user?.role === role;
  }, [auth.user]);

  const hasAnyRoles = useCallback((roles: string[]) => {
    return auth.user?.role.toLowerCase() === 'admin' ? true :
            roles?.includes('*') ? true :
              roles?.some(role => auth.user?.role === role) || false;
  }, [auth.user]);

  const hasPermission = useCallback((permission: string) => {
    return auth.user?.role.toLowerCase() === 'admin' ? true :
            permission === '*' ? true :
              auth.user?.permissions?.includes(permission) || false;
  }, [auth.user]);

  const hasAnyPermissions = useCallback((permissions: string[]) => {
    return auth.user?.role.toLowerCase() === 'admin' ? true :
            permissions?.includes('*') ? true :
              permissions?.some(permission => auth.user?.permissions?.includes(permission)) || false;
  }, [auth.user]);

  const permissions = useMemo(() => auth.user?.permissions || [], [auth.user]);
  const role = useMemo(() => auth.user?.role || '', [auth.user]);
  const isAuthenticated = useMemo(() => auth.isAuthenticated, [auth.isAuthenticated]);
  const user = useMemo(() => auth.user, [auth.user]);

  return useMemo(()=> ({
    login,
    logout,
    hasPermission,
    hasRole,
    hasAnyRoles,
    hasAnyPermissions,
    permissions,
    role,
    isAuthenticated,
    user
  }), [login,
    logout,
    hasPermission,
    hasRole,
    hasAnyRoles,
    hasAnyPermissions,
    permissions,
    role,
    isAuthenticated,
    user]);
};

export default useAppAuth;
