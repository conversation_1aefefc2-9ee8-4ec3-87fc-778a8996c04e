import { useEffect, useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import {
  Button,
  Typography,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Space,
  message,
  Spin,
} from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router";
import { Prime, PrimeStatus, PrimeType } from "~/models/Prime";
import { getPrimeById, updatePrime } from "~/services/api/primeService";
import dayjs from "dayjs";

const { Option } = Select;

const EditPrimePage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const fetchPrime = async () => {
      if (!id) return;

      setLoading(true);
      try {
        const prime = await getPrimeById(id);
        form.setFieldsValue({
          ...prime,
          date: prime.date ? dayjs(prime.date) : null,
        });
      } catch (error) {
        message.error("Erreur lors du chargement des détails de la prime");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrime();
  }, [id, form]);

  const handleBack = () => {
    navigate("/primes");
  };

  const onFinish = async (values: any) => {
    if (!id) return;

    setSubmitting(true);
    try {
      // Format the date to ISO string
      const formattedValues = {
        ...values,
        date: values.date?.toISOString(),
      };

      await updatePrime(id, formattedValues);
      message.success("Prime mise à jour avec succès");
      navigate("/primes");
    } catch (error) {
      message.error("Erreur lors de la mise à jour de la prime");
      console.error(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <PageContainer
      header={{
        title: (
          <Typography.Title level={3} className="w-full">
            Modifier la Prime
          </Typography.Title>
        ),
        onBack: handleBack,

        backIcon: <ArrowLeftOutlined />,
      }}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ maxWidth: 800, margin: "0 auto" }}
        >
          <Form.Item
            name="name"
            label="Nom"
            rules={[{ required: true, message: "Veuillez saisir un nom" }]}
          >
            <Input placeholder="Nom de la prime" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Type"
            rules={[{ required: true, message: "Veuillez sélectionner un type" }]}
          >
            <Select placeholder="Sélectionnez un type de prime">
              <Option value={PrimeType.ANNUAL}>Annuelle</Option>
              <Option value={PrimeType.PERFORMANCE}>Performance</Option>
              <Option value={PrimeType.PROJECT}>Projet</Option>
              <Option value={PrimeType.EXCEPTIONAL}>Exceptionnelle</Option>
              <Option value={PrimeType.OTHER}>Autre</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="Montant"
            rules={[{ required: true, message: "Veuillez saisir un montant" }]}
          >
            <InputNumber
              style={{ width: "100%" }}
              formatter={(value) => `${value} €`}
              parser={(value) => {
                const parsed = value ? parseFloat(value.replace(/[^\d.]/g, '')) : 0;
                return parsed;
              }}
              min={0}
              placeholder="Montant de la prime"
            />
          </Form.Item>

          <Form.Item
            name="date"
            label="Date"
            rules={[{ required: true, message: "Veuillez sélectionner une date" }]}
          >
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item
            name="status"
            label="Statut"
            rules={[{ required: true, message: "Veuillez sélectionner un statut" }]}
          >
            <Select placeholder="Sélectionnez un statut">
              <Option value={PrimeStatus.DRAFT}>Brouillon</Option>
              <Option value={PrimeStatus.PENDING}>En attente</Option>
              <Option value={PrimeStatus.APPROVED}>Approuvée</Option>
              <Option value={PrimeStatus.REJECTED}>Rejetée</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={submitting}>
                Enregistrer
              </Button>
              <Button onClick={handleBack}>Annuler</Button>
            </Space>
          </Form.Item>
        </Form>
      </Spin>
    </PageContainer>
  );
};

export default EditPrimePage;
