export interface Prime {
  id: string;
  name: string;
  type: PrimeType;
  amount: number;
  date: string;
  status: PrimeStatus;
  fileName?: string;
  periodicite?: Periodicite;
  avecUnite?: boolean;
  description?: string;
  periode?: string;
  unites?: UniteVersement[];
}

export enum PrimeType {
  ANNUAL = 'annual',
  PERFORMANCE = 'performance',
  PROJECT = 'project',
  EXCEPTIONAL = 'exceptional',
  OTHER = 'other'
}

export enum Periodicite {
  ANNUELLE = 'annuelle',
  TRIMESTRIELLE = 'trimestrielle',
  MENSUELLE = 'mensuelle'
}

export enum PrimeStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface UniteVersement {
  id: string;
  codeUnite: string;
  montantVerset: number;
}

export interface PrimeListResponse {
  data: Prime[];
  total: number;
}

export interface PrimeUploadResponse {
  success: boolean;
  data: Prime[];
}

export interface PrimeTypeMock {
  id: string;
  code: string;
  libelle: string;
  periodicite: Periodicite;
  description: string;
  avecUnite: boolean;
}

// Interface pour les types de primes selon l'API
export interface TypePrime {
  codeTypePrime: string;
  libelle: string;
  description: string;
  periodicite: string;
  nom_table_prime: string;
  avecUnite: boolean;
}

export interface FonctionsAgent {
  codeFonction: string;
  libelle: string;
  codeTypePrime: string;
  coefficient: number;
  dateDebut: string;
  createdAt: string;
  updatedAt: string;
}
