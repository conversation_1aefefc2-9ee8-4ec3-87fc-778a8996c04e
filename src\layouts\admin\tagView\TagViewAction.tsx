import { SettingOutlined } from "@ant-design/icons"
import { Dropdown } from "antd"
import { useDispatch } from "react-redux"
import { removeAllTag, removeOtherTag } from "~/redux/slices/tagSlice"
import { useTranslation } from "react-i18next"
import { PATH_ADMIN } from "~/constants"
import { useNavigate } from "react-router"

function TagsViewAction() {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation()

  return (
    <Dropdown
      menu={{
        items: [
          {
            key: "0",
            onClick: () => {
              dispatch(removeOtherTag(undefined))
            },
            label: t("tagsView.closeOther"),
          },
          {
            key: "1",
            onClick: () => {
              dispatch(removeAllTag(undefined))
              navigate(PATH_ADMIN.root)
            },
            label: t("tagsView.closeAll"),
          },
        ],
      }}
    >
        <SettingOutlined className="mr-2" />
    </Dropdown>
  )
}

export default TagsViewAction
