import { Fonction, CoefficientConfig } from '../../models/Agent';
import { PrimeType } from '../../models/Prime';

export const fonctions: Fonction[] = [
  {
    code: 'chef_service',
    libelle: 'Chef de service'
  },
  {
    code: 'responsable_unite',
    libelle: 'Responsable d\'unité'
  },
  {
    code: 'agent_terrain',
    libelle: 'Agent de terrain'
  },
  {
    code: 'verificateur',
    libelle: 'Vérificateur'
  },
  {
    code: 'directeur_adjoint',
    libelle: 'Directeur adjoint'
  },
  {
    code: 'controleur',
    libelle: 'Contrôleur'
  },
  {
    code: 'inspecteur',
    libelle: 'Inspecteur'
  },
  {
    code: 'secretaire',
    libelle: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    code: 'chauffeur',
    libelle: 'Chauffeur'
  }
];

export const coefficientsConfig: CoefficientConfig[] = [
  // Prime Annuelle
  { fonction: 'chef_service', primeType: PrimeType.ANNUAL, coefficient: 1.5 },
  { fonction: 'responsable_unite', primeType: PrimeType.ANNUAL, coefficient: 1.2 },
  { fonction: 'agent_terrain', primeType: PrimeType.ANNUAL, coefficient: 1.0 },
  { fonction: 'verificateur', primeType: PrimeType.ANNUAL, coefficient: 1.1 },
  { fonction: 'directeur_adjoint', primeType: PrimeType.ANNUAL, coefficient: 1.8 },
  { fonction: 'controleur', primeType: PrimeType.ANNUAL, coefficient: 1.3 },
  { fonction: 'inspecteur', primeType: PrimeType.ANNUAL, coefficient: 1.4 },
  { fonction: 'secretaire', primeType: PrimeType.ANNUAL, coefficient: 0.9 },
  { fonction: 'chauffeur', primeType: PrimeType.ANNUAL, coefficient: 0.8 },

  // Prime de Performance
  { fonction: 'chef_service', primeType: PrimeType.PERFORMANCE, coefficient: 2.0 },
  { fonction: 'responsable_unite', primeType: PrimeType.PERFORMANCE, coefficient: 1.5 },
  { fonction: 'agent_terrain', primeType: PrimeType.PERFORMANCE, coefficient: 1.2 },
  { fonction: 'verificateur', primeType: PrimeType.PERFORMANCE, coefficient: 1.3 },
  { fonction: 'directeur_adjoint', primeType: PrimeType.PERFORMANCE, coefficient: 2.5 },
  { fonction: 'controleur', primeType: PrimeType.PERFORMANCE, coefficient: 1.6 },
  { fonction: 'inspecteur', primeType: PrimeType.PERFORMANCE, coefficient: 1.7 },
  { fonction: 'secretaire', primeType: PrimeType.PERFORMANCE, coefficient: 1.0 },
  { fonction: 'chauffeur', primeType: PrimeType.PERFORMANCE, coefficient: 0.9 },

  // Prime de Projet
  { fonction: 'chef_service', primeType: PrimeType.PROJECT, coefficient: 1.8 },
  { fonction: 'responsable_unite', primeType: PrimeType.PROJECT, coefficient: 1.4 },
  { fonction: 'agent_terrain', primeType: PrimeType.PROJECT, coefficient: 1.1 },
  { fonction: 'verificateur', primeType: PrimeType.PROJECT, coefficient: 1.2 },
  { fonction: 'directeur_adjoint', primeType: PrimeType.PROJECT, coefficient: 2.2 },
  { fonction: 'controleur', primeType: PrimeType.PROJECT, coefficient: 1.5 },
  { fonction: 'inspecteur', primeType: PrimeType.PROJECT, coefficient: 1.6 },
  { fonction: 'secretaire', primeType: PrimeType.PROJECT, coefficient: 1.0 },
  { fonction: 'chauffeur', primeType: PrimeType.PROJECT, coefficient: 0.8 },

  // Prime Exceptionnelle
  { fonction: 'chef_service', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.6 },
  { fonction: 'responsable_unite', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.3 },
  { fonction: 'agent_terrain', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.0 },
  { fonction: 'verificateur', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.1 },
  { fonction: 'directeur_adjoint', primeType: PrimeType.EXCEPTIONAL, coefficient: 2.0 },
  { fonction: 'controleur', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.4 },
  { fonction: 'inspecteur', primeType: PrimeType.EXCEPTIONAL, coefficient: 1.5 },
  { fonction: 'secretaire', primeType: PrimeType.EXCEPTIONAL, coefficient: 0.9 },
  { fonction: 'chauffeur', primeType: PrimeType.EXCEPTIONAL, coefficient: 0.8 },

  // Autre Prime
  { fonction: 'chef_service', primeType: PrimeType.OTHER, coefficient: 1.2 },
  { fonction: 'responsable_unite', primeType: PrimeType.OTHER, coefficient: 1.0 },
  { fonction: 'agent_terrain', primeType: PrimeType.OTHER, coefficient: 0.9 },
  { fonction: 'verificateur', primeType: PrimeType.OTHER, coefficient: 1.0 },
  { fonction: 'directeur_adjoint', primeType: PrimeType.OTHER, coefficient: 1.5 },
  { fonction: 'controleur', primeType: PrimeType.OTHER, coefficient: 1.1 },
  { fonction: 'inspecteur', primeType: PrimeType.OTHER, coefficient: 1.2 },
  { fonction: 'secretaire', primeType: PrimeType.OTHER, coefficient: 0.8 },
  { fonction: 'chauffeur', primeType: PrimeType.OTHER, coefficient: 0.7 }
];

export default fonctions;
