import { EditOutlined } from "@ant-design/icons"
import { ProCard, ProForm, ProFormGroup, ProTable, TableDropdown } from "@ant-design/pro-components"
import { Button, message, Space, Tabs } from "antd"
import { waitTime } from "src/services/utils"
// import accounts from "assets/imgs/account.svg"
import { useEffect, useRef, useState } from "react"
import AddAgents from "./AddAgents"
import EtatCivil from "./EtatCivil"
import SituationAdministrative from "./SituationAdministrative"
import SituationFinanciere from "./SituationFinanciere"
import SituationMatrimoniale from "./SituationMatrimoniale"

function TableAgents({ dataSource }: any) {
  const [data, setData] = useState(dataSource)
  const [imageUrl, setImageUrl] = useState()

  useEffect(() => {
    // console.log("Data =========> ", data);
  }, [data])

  const deleteRow = e => {
    const d = data.filter(item => item.id !== e)
    setData(d)
  }

  const [detail, setDetail] = useState(null)
  const [select, setSelect] = useState(false)

  const showDetail = id => {
    setSelect(true)
    setDetail(data.filter(d => d.id === id).pop())
    setImageUrl(detail?.profil)
  }

  const [state, setState] = useState("read")

  const columns = [
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Matricule",
      dataIndex: "matricule",
      key: "matricule",
    },
    {
      title: "Nom",
      dataIndex: "nom",
      key: "nom",
    },
    {
      title: "Prénom",
      dataIndex: "prenom",
      key: "prenom",
    },
    {
      title: "Actions",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => showDetail(record.id)}>
            View
          </Button>
          <Button type="link" onClick={() => deleteRow(record.id)}>
            Delete
          </Button>
          <TableDropdown
            key="actionGroup"
            menus={
              [
                // { key: 'copy', name: 'Copy' },
                // { key: 'delete', name: 'Cute' },
              ]
            }
          />
        </Space>
      ),
    },
  ]

  // const handlePreview = async file => {
  //   if (!file.url && !file.preview) {
  //     file.preview = await getBase64(file.originFileObj)
  //   }

  //   setPreviewImage(file.url || file.preview)
  //   setPreviewOpen(true)
  // }

  const items = [
    {
      key: "1",
      label: "Fiche de renseignement",
      children: (
        <div>
          <ProCard title="Etat civile" headerBordered>
            <EtatCivil imageUrl={imageUrl} onImageChange={url => setImageUrl(url)} />
          </ProCard>

          <ProCard
            title="Situation Administrative"
            headerBordered
            collapsible
            defaultCollapsed={true}
            style={{
              gap: "0 32px",
            }}
          >
            <SituationAdministrative />
          </ProCard>

          <ProCard
            title="Situation Financière"
            headerBordered
            collapsible
            defaultCollapsed="true"
            style={{
              gap: "0 32px",
            }}
          >
            <SituationFinanciere />
          </ProCard>
        </div>
      ),
    },
    {
      key: "2",
      label: "Situation Matrimoniale",
      children: (
        <div>
          <SituationMatrimoniale />
        </div>
      ),
    },
    // {
    //   key: "3",
    //   label: "Actes Avancement",
    //   children: (
    //     <div>
    //       <ProFormMoney
    //         label="小数点精度"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 2 }}
    //         customSymbol="💰"
    //       />
    //       <ProFormMoney
    //         label="小数点精度-0"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 0 }}
    //         customSymbol="💰"
    //       />
    //     </div>
    //   ),
    // },
    // {
    //   key: "4",
    //   label: "Mutations/Nominations",
    //   children: (
    //     <div>
    //       <ProFormMoney
    //         label="小数点精度"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 2 }}
    //         customSymbol="💰"
    //       />
    //       <ProFormMoney
    //         label="小数点精度-0"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 0 }}
    //         customSymbol="💰"
    //       />
    //     </div>
    //   ),
    // },
    // {
    //   key: "5",
    //   label: "Autres",
    //   children: (
    //     <div>
    //       <ProFormMoney
    //         label="小数点精度"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 2 }}
    //         customSymbol="💰"
    //       />
    //       <ProFormMoney
    //         label="小数点精度-0"
    //         name="amount6"
    //         initialValue={2222222222.222222}
    //         fieldProps={{ precision: 0 }}
    //         customSymbol="💰"
    //       />
    //     </div>
    //   ),
    // },
  ]

  const formRef = useRef()
  const [readonly, setReadonly] = useState(true)

  return (
    <div className="my-3">
      <ProCard
        bordered
        boxShadow
        className="mb-5"
        title="Tableau Agents"
        collapsible
        // tooltip="Liste des utilisateurs"
      >
        {/* <Table
          dataSource={data}
          columns={columns}
          // onRow={(record) => ({
          //   onClick: () => handleRowClick(record),
          // })}
        /> */}
        <ProTable
          columns={columns}
          request={(params, sorter, filter) => {
            // console.log("params, sorter, filter", params, sorter, filter)
            return Promise.resolve({
              data: data,
              success: true,
            })
          }}
          cardProps={{ title: "Liste des agents", bordered: true }}
          // headerTitle={

          // }
          toolbar={{
            search: {
              onSearch: value => {
                alert(value)
              },
            },
            actions: [
              <ProFormGroup>
                <AddAgents />
              </ProFormGroup>,
            ],
          }}
          rowKey="key"
          search={false}
        />
      </ProCard>

      {select ? (
        <ProCard
          bordered
          boxShadow
          title="Formulaire"
          extra={[
            <Button
              key="primary"
              type="link"
              onClick={() => {
                setReadonly(e => !e)
              }}
            >
              <EditOutlined />
            </Button>,
            <Button
              key="primary"
              type="link"
              onClick={() => {
                setSelect(false)
              }}
            >
              X
            </Button>,
          ]}
        >
          <ProForm
            // grid
            onFinish={async values => {
              await waitTime(2000)
              values.profil = imageUrl
              console.log(values)
              const val1 = await formRef.current?.validateFields()
              console.log("validateFields:", val1)
              const val2 = await formRef.current?.validateFieldsReturnFormatValue?.()
              console.log("validateFieldsReturnFormatValue:", val2)
              // console.log("imageUrl:", imageUrl)
              message.success("提交成功")
            }}
            formRef={formRef}
            params={{ id: "100" }}
            formKey="base-form-use-demo"
            readonly={readonly}
            request={async () => {
              await waitTime(100)
              return {
                name: "iciiiiiiiiiiiiiiiiiiiii",
                useMode: "chapter",
              }
            }}
            autoFocusFirstInput
            initialValues={detail}
            // dateFormatter="string"
            // onValuesChange={(changeValues) => console.log(changeValues)}
            // submitter={{
            //   render: (props, doms) => {
            //     // console.log(props);
            //     if (readonly) return null
            //     else return [
            //       <Button
            //         type="default"
            //         key="rest"
            //         onClick={() => props.form?.resetFields()}
            //       >
            //         Réinitialiser
            //       </Button>,
            //       <Button
            //         type="primary"
            //         key="submit"
            //         onClick={() => props.form?.submit?.()}
            //       >
            //         Modifier
            //       </Button>,
            //     ];
            //   },
            // }}
            submitter={!readonly}
            grid
          >
            <Tabs defaultActiveKey="1" tabPosition="left" items={items} />
          </ProForm>
        </ProCard>
      ) : null}
    </div>
  )
}

export default TableAgents
