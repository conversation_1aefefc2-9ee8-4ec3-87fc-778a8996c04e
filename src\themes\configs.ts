import { theme as antdTheme, ThemeConfig } from "antd"
import { createInstance, CustomToken } from "antd-style";
import { THEMES, PREFIXCLS } from "~/constants";
import Storage from "~/services/Storage";

export interface AppCustomToken extends CustomToken {
  accessColor: string;
  rowProps: object;
  carouselProps: object;
}

export const getCustomTokenConfig = (/* { token, isDarkMode }:CustomTokenParams */): AppCustomToken => {

  return {
    accessColor: '#12388f',
    rowProps: {
      gutter: [
        { xs: 8, sm: 16, md: 24, lg: 32 },
        { xs: 8, sm: 16, md: 24, lg: 32 },
      ],
    },
    carouselProps: {
      autoplay: true,
      dots: true,
      dotPosition: "bottom",
      infinite: true,
      slidesToShow: 3,
      slidesToScroll: 1,
    },
  }
}


export const getThemeConfig = (appearance:any = "light"): ThemeConfig => {
  Storage.persistTheme(appearance)
  const theme = THEMES[appearance]
  const COLOR = theme.palette

  return {
    algorithm: appearance === "light" ? antdTheme.defaultAlgorithm : antdTheme.darkAlgorithm,
    token: {
      colorPrimary: theme.primary,
      colorInfo: theme.secondary,
      colorSuccess: theme.success,
      colorError: theme.error,
      colorWarning: theme.warning,
    },
    components: {
      Breadcrumb: {
        // linkColor: 'rgba(0,0,0,.8)',
        // itemColor: 'rgba(0,0,0,.8)',
      },
      Calendar: {
        colorBgContainer: 'none',
      },
      Card: {
        colorBorderSecondary: COLOR['borderColor'],
      },
      Carousel: {
        colorBgContainer: COLOR['800'],
        dotWidth: 8,
      },
      Rate: {
        colorFillContent: COLOR['100'],
        colorText: COLOR['600'],
      },
      Segmented: {
        colorBgLayout: COLOR['100'],
        borderRadius: 6,
        colorTextLabel: '#000000',
      },
      Table: {
        borderColor: COLOR['100'],
        colorBgContainer: 'none',
        headerBg: 'none',
        rowHoverBg: COLOR['50'],
      },
      Tabs: {
        colorBorderSecondary: COLOR['100'],
      },
      Timeline: {
        dotBg: 'none',
      },
      Typography: {
        colorLink: COLOR['500'],
        colorLinkActive: COLOR['700'],
        colorLinkHover: COLOR['300'],
        linkHoverDecoration: 'underline',
      },
    },
    // components: {
    //   Alert: {
    //     colorInfoBg: '#dfefff',
    //     colorText: theme.black,
    //     colorTextHeading: theme.black,
    //     marginSM: 15,
    //     paddingContentVerticalSM: 8,
    //     paddingMD: 15,
    //     paddingContentHorizontalLG: 15,
    //   },
    //   Badge: {
    //     colorPrimary: theme.primary,
    //     colorTextPlaceholder: '#d9d9d9',
    //     fontSizeSM: remToPixels(theme.fontSizes.xxs),
    //   },
    //   Breadcrumb: {
    //     fontSizeIcon: 10,
    //     colorTextDescription: theme.breadcrumb,
    //   },
    //   Calendar: {
    //     controlHeightSM: remToPixels(theme.heights.xs) / 1.5,
    //   },
    //   Card: {
    //     colorTextHeading: theme.textMain,
    //     padding: 20,
    //     paddingLG: 20,
    //     boxShadowTertiary: theme.boxShadow,
    //   },
    //   Divider: {
    //     colorSplit: 'rgba(0, 0, 0, 0.06)',
    //   },
    //   Empty: {
    //     controlHeightLG: remToPixels(theme.heights.sm),
    //   },
    //   Menu: {
    //     controlHeightLG: remToPixels(theme.heights.md),
    //     fontSize: remToPixels(theme.fontSizes.xs),
    //     colorFillAlter: `rgba(${themeObject['light'].rgb.primary6}, 0.05)`,
    //     colorBgContainer: 'unset',
    //     controlItemBgActive: 'unset',
    //     colorBgTextHover: 'transparent',
    //     itemSelectedColor: theme.textSiderPrimary,
    //     colorText: theme.textSiderSecondary,
    //     colorSplit: 'transparent',
    //     activeBarWidth: 2,
    //     marginXXS: 4,
    //     controlHeightSM: 30,
    //     itemMarginInline: 0,
    //   },
    //   Pagination: {
    //     colorPrimary: theme.primary,
    //     controlItemBgActiveDisabled: '#e6e6e6',
    //     itemSizeSM: 24,
    //     controlHeightLG: remToPixels(theme.heights.sm),
    //   },
    //   Popconfirm: {
    //     fontWeightStrong: theme.fontWeights.semibold,
    //     colorPrimary: theme.primary,
    //   },
    //   Popover: {
    //     zIndexPopup: 2000,
    //     controlHeight: 34,
    //     sizePopupArrow: 20,
    //   },
    //   Progress: {
    //     marginXS: 0,
    //     colorFillSecondary: theme.backgroundColorBase,
    //   },
    //   Rate: {
    //     starColor: '#ffc24b',
    //     colorFillContent: theme.split,
    //   },
    //   Result: {
    //     fontSizeHeading3: remToPixels(theme.fontSizes.xxl),
    //     lineHeightHeading3: 1.8,
    //   },
    //   Slider: {
    //     colorPrimaryBorder: theme.primary3,
    //     colorPrimary: theme.primary4,
    //     colorPrimaryBorderHover: theme.primary4,
    //     colorFillSecondary: theme.sliderFillColor,
    //     colorBorderSecondary: theme.sliderFillColor,
    //     colorFillContentHover: theme.sliderFillColor,
    //     colorFillTertiary: theme.backgroundColorBase,
    //     handleSize: 11,
    //     handleSizeHover: 11,
    //     handleLineWidth: 2,
    //     handleLineWidthHover: 2,
    //     colorTextDisabled: theme.disabled,
    //   },
    //   Spin: {
    //     controlHeight: remToPixels(theme.heights.xs),
    //     controlHeightLG: remToPixels(theme.heights.sm),
    //     colorPrimary: theme.primary,
    //   },
    //   Steps: {
    //     iconSize: remToPixels(theme.heights.xs),
    //     iconFontSize: remToPixels(theme.fontSizes.lg),
    //     controlHeight: remToPixels(theme.heights.md),
    //     controlHeightSM: remToPixels(theme.heights.xxs),
    //     fontSizeHeading3: remToPixels(theme.fontSizes.xxl),
    //     colorPrimary: theme.primary,
    //   },
    //   Switch: {
    //     controlHeight: remToPixels(theme.heights.xs),
    //     colorPrimary: theme.primary,
    //     colorWhite: theme.background,
    //     lineHeight: 1.375,
    //     colorPrimaryBorder: theme.primary1,
    //     opacityLoading: 0.4,
    //   },
    //   Table: {
    //     borderRadiusLG: 0,
    //     colorBorderSecondary: '#b3cbe1',
    //     colorTextHeading: theme.primary,
    //     colorFillAlter: `rgba(${theme.rgb.primary}, 0.05)`,
    //     controlItemBgActive: theme.primary1,
    //     colorSplit: 'rgba(0, 0, 0, 0.15)',
    //     controlItemBgActiveHover: `rgba(${theme.rgb.primary}, 0.12)`,
    //   },
    //   Tabs: {
    //     colorPrimaryHover: theme.primary5,
    //     colorPrimary: theme.primary,
    //     colorPrimaryActive: theme.primary7,
    //     colorTextDisabled: theme.disabled,
    //     colorBorderSecondary: theme.split,
    //   },
    //   InputNumber: {
    //     colorPrimary: theme.primary5,
    //   },
    //   Layout: {
    //     controlHeight: 34,
    //     controlHeightLG: 12.8,
    //     bodyBg: theme.layoutBodyBg,
    //     headerBg: theme.layoutSiderBg,
    //   },
    //   Tree: {
    //     controlHeightSM: remToPixels(theme.heights.xxs),
    //   },
    //   Checkbox: {
    //     colorBgContainer: 'transparent',
    //     colorPrimary: theme.primary,
    //     colorTextDisabled: theme.disabled,
    //     marginXS: 8,
    //   },
    //   Tag: {
    //     fontSize: remToPixels(theme.fontSizes.xs),
    //   },
    //   Select: {
    //     fontSizeSM: remToPixels(theme.fontSizes.xs),
    //     colorTextPlaceholder: theme.textMain,
    //     colorTextTertiary: theme.icon,
    //     colorTextQuaternary: theme.textMain, // arrow color
    //     colorFillSecondary: theme.backgroundColorBase,
    //     colorIcon: theme.icon,
    //     colorIconHover: theme.iconHover,
    //     colorPrimary: theme.primary,
    //     colorPrimaryHover: theme.primary5,
    //     controlItemBgActive: theme.primary1,
    //     controlItemBgHover: theme.itemHoverBg,
    //   },
    //   Skeleton: {
    //     controlHeightXS: 16,
    //     controlHeightSM: remToPixels(theme.heights.xs),
    //     controlHeight: remToPixels(theme.heights.md),
    //     controlHeightLG: remToPixels(theme.heights.lg),
    //     gradientFromColor: 'rgba(190, 190, 190, 0.2)',
    //   },
    //   Notification: {
    //     zIndexPopup: 9999,
    //     width: 36 * 16,
    //     paddingMD: 20,
    //     paddingLG: 20,
    //     paddingContentHorizontalLG: 2 * 16,
    //     lineHeightLG: 2,
    //     colorSuccess: theme.success,
    //     colorInfo: theme.primary,
    //     colorWarning: theme.warning,
    //     colorError: theme.error,
    //     colorIconHover: 'rgba(0, 0, 0, 0.67)',
    //     fontSizeLG: remToPixels(theme.fontSizes.xxl),
    //     marginSM: 0,
    //     boxShadow: theme.modalBoxShadow,
    //     controlHeightLG: 15 / 0.55,
    //   },
    //   Input: {
    //     colorTextPlaceholder: theme.inputPlaceholder,
    //     colorTextDisabled: theme.disabled,
    //     colorPrimaryHover: theme.primary5,
    //     fontWeightStrong: theme.fontWeights.semibold,
    //     colorFillAlter,
    //     controlOutlineWidth: 0,
    //   },
    //   Form: {
    //     marginLG: 16,
    //     colorInfoBorderHover: theme.primary5,
    //   },
    //   Avatar: {
    //     colorTextPlaceholder: theme.avatarBg,
    //     colorBorderBg: theme.white,
    //     controlHeightSM: remToPixels(theme.heights.xxs),
    //     controlHeight: remToPixels(theme.heights.xs),
    //     controlHeightLG: remToPixels(theme.heights.sm),
    //   },
    //   Button: {
    //     colorPrimary: theme.primary,
    //     colorLinkHover: theme.primary5,
    //     controlOutlineWidth: 0,
    //   },
    //   Radio: {
    //     colorPrimary: theme.primary,
    //     controlItemBgActiveDisabled: '#e6e6e6',
    //   },
    //   Modal: {
    //     colorTextDescription: theme.icon,
    //     colorIcon: theme.icon,
    //     boxShadow: theme.modalBoxShadow,
    //     lineHeight: 1.57,
    //   },
    //   DatePicker: {
    //     colorIcon: theme.textLight,
    //     colorTextDisabled: theme.textLight,
    //     colorPrimary: '#1c68a6',
    //     controlItemBgActive: theme.primary1,
    //     colorTextPlaceholder: theme.inputPlaceholder,
    //     fontWeightStrong: theme.fontWeights.medium,
    //     controlHeightSM: remToPixels(theme.heights.xxs),
    //     controlHeightLG: remToPixels(theme.heights.sm),
    //     padding: 13,
    //     paddingXXS: 2,
    //   },
    //   Dropdown: {
    //     paddingXXS: 0,
    //     fontSizeIcon: 10,
    //     controlHeight: 34,
    //   },
    //   Upload: {
    //     colorFillAlter: `rgba(${theme.rgb.primary}, 0.05)`,
    //     colorPrimaryHover: theme.primary5,
    //   },
    // },
  }
}

export const styleInstance = createInstance<AppCustomToken>({
  // **** Style Generation Related **** //

  key: PREFIXCLS + '-css', // Set the prefix for generating hash class names, the result will be .abc-xxxx
  speedy: false, // Currently, the default cssom insertion method in the cssinjs solution is not very compatible with qiankun micro-apps, so it is recommended to disable it
  hashPriority: 'high', // Set the style selector that generates hash to :where selector to reduce weight. This allows user-defined styles to override component styles

  // ***** Theme Related ***** //
  // Configure the props passed to the ThemeProvider by default, and this Provider can also be overridden by external props
  // The configured value will also become the default value consumed by related methods, so there is no need to wrap ThemeProvider to consume the default value

  prefixCls: PREFIXCLS, // Set the class name prefix for antd components, for example, the type of Button will be .tna-btn
  iconPrefixCls: PREFIXCLS + "-icons",
  container: window.document.body
})

export const {
  // **** Core Style Methods **** //
  createStyles,
  // createStylish,
  createGlobalStyle,

  // **** Basic Style Methods **** //
  cx,
  css,
  keyframes,
  // injectGlobal,

  //**** Style Sheet Management  **** //
  styleManager,

  // **** Data Container   **** //
  useTheme,
  // StyleProvider,
  ThemeProvider,
} = styleInstance;
