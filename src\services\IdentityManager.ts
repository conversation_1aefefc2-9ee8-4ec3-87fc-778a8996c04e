import { v7 as uuidv7, v6 as uuidv6, v4 as uuidv4 } from "uuid"
import AppError from "./AppError"

export default class IdentityManager {
  private static readonly s_ids: Set<unknown> = new Set()

  // Returns a new unused unique identifier.
  static fetchUUID(nbrVersion: number = 4) {
    const loadUuidBy = (nbr?: number) => {
      switch (nbr) {
        case 7:
          return uuidv7()
        case 6:
          return uuidv6()
        default:
          return uuidv4()
      }
    }

    let uuid = loadUuidBy(nbrVersion)

    while (IdentityManager.s_ids.has(uuid)) {
      uuid = loadUuidBy(nbrVersion)
    }

    IdentityManager.s_ids.add(uuid)

    return uuid
  }

  private readonly ids: Set<unknown>
  constructor() {
    this.ids = new Set()
  }

  // Returns a new unused unique identifier.
  fetch() {
    return IdentityManager.fetchUUID()
  }

  // Registers an identifier as used. Must throw if identifier is already used.
  set(id: any) {
    if (this.ids.has(id)) {
      throw new AppError(`ID ${id} has already been used.`)
    }

    this.ids.add(id)
  }

  // Resets all used identifiers to unused.
  reset() {
    this.ids.clear()
  }
}
