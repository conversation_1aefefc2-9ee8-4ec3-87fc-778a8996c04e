import { extend } from 'umi-request';
import { PROXY1 } from '~/constants';
import AppError from './AppError';
import Storage from './Storage';

const prefix = !import.meta.env.VITE_USE_MIRAGE ? PROXY1 : undefined;

// Appel d'umi-request
const request = extend({
  prefix,
  timeout: 15000, // Temps maximum pour une requête
  headers: { 'Content-Type': 'application/json' },
  errorHandler: (error) => {
    if (import.meta.env.DEV) console.dir(error, {depth: null});
    throw new AppError(error.response?.statusText || error.message, error.data || error, error.response?.status);
  },
});

// Add request interceptor
request.interceptors.request.use((url, options) => {
  const token = Storage.readToken();
  const headers = options.headers as Record<string, string>;

  if (!options.noToken && import.meta.env.PROD && token) {
    headers.Authorization = `Bearer ${token}`;
  }

  if (import.meta.env.VITE_USE_MIRAGE){
    url = (options.devPrefix && !options.prefix && !url.startsWith("http") && !url.startsWith(options.devPrefix)) ? `${options.devPrefix}${url}`: url;
    delete headers.Authorization;
  } else if (!options.prefix) {
    url = (!url.startsWith("http") && !url.startsWith(PROXY1)) ? `${PROXY1}${url}` : url;
  }

  if (import.meta.env.DEV && import.meta.env.VITE_USE_HTTP_LOG) {
    console.info("=====> REQUEST INTERCEPTOR <=====\n")
    console.log('url', url)
    console.log('headers', JSON.stringify(headers))
    console.dir(options)
    console.info("=====> END REQUEST INTERCEPTOR <=====\n")
  }

  return {
    url,
    options: { ...options, headers },
  };
});

// Add response interceptor
request.interceptors.response.use(async (response) => {
  if (import.meta.env.DEV && import.meta.env.VITE_USE_HTTP_LOG) {
    console.info("=====> RESPONSE INTERCEPTOR <=====\n")
    console.dir(response)
    console.info("=====> END RESPONSE INTERCEPTOR <=====\n")
  }
  return response;
});

export default request;
