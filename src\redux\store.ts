import { configureStore, combineReducers } from "@reduxjs/toolkit"
import appSlice from "./slices/appSlice"
import authSlice from "./slices/authSlice"
import tagSlice from "./slices/tagSlice"

// Combine reducers
const rootReducer = combineReducers({
  app: appSlice.reducer,
  auth: authSlice.reducer,
  tag: tagSlice.reducer,
})

// Configure store with optimized middleware
export const store = configureStore({
  reducer: rootReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
      // serializableCheck: {
      //   // Ignore certain actions or paths that may contain non-serializable data
      //   ignoredActions: [],
      //   ignoredActionPaths: ['payload.callback'],
      //   ignoredPaths: [],
      // },
    }),
  // Enable Redux DevTools only in development
  devTools: process.env.NODE_ENV !== "production",
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
