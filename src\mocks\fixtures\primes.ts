import { PrimeType, Periodicite, PrimeTypeMock } from '../../models/Prime';
import IdentityManager from '../../services/IdentityManager';

export const primeTypes: PrimeTypeMock[] = [
  {
    id: IdentityManager.fetchUUID(),
    code: PrimeType.ANNUAL,
    libelle: 'Prime Annuelle',
    periodicite: Periodicite.ANNUELLE,
    description: 'Prime annuelle pour tous les agents',
    avec_unite: false
  },
  {
    id: IdentityManager.fetchUUID(),
    code: PrimeType.PERFORMANCE,
    libelle: 'Prime de Performance',
    periodicite: Periodicite.TRIMESTRIELLE,
    description: 'Prime trimestrielle basée sur les performances',
    avec_unite: true
  },
  {
    id: IdentityManager.fetchUUID(),
    code: PrimeType.PROJECT,
    libelle: 'Prime de Projet',
    periodicite: Periodicite.MENSUELLE,
    description: 'Prime mensuelle pour les projets',
    avec_unite: false
  },
  {
    id: IdentityManager.fetchUUID(),
    code: PrimeType.EXCEPTIONAL,
    libelle: 'Prime Exceptionnelle',
    periodicite: Periodicite.ANNUELLE,
    description: 'Prime exceptionnelle pour les agents',
    avec_unite: true
  },
  {
    id: IdentityManager.fetchUUID(),
    code: PrimeType.OTHER,
    libelle: 'Autre Prime',
    periodicite: Periodicite.MENSUELLE,
    description: 'Autre prime',
    avec_unite: false
  }
];

export default primeTypes;
