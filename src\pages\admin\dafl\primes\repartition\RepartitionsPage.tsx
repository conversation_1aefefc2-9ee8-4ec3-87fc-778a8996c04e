import { useLoaderD<PERSON>, useNavigate } from "react-router";
import { useRequest } from "ahooks";
import { PRIMES_PATH, getRepartitionsListApi } from "..";
import { ProColumns } from "@ant-design/pro-components";
import { ArrowLeftOutlined, PlusCircleOutlined, FilterOutlined, TableOutlined, DatabaseOutlined, SettingOutlined } from "@ant-design/icons";
import { Button, Select, Space, Typography } from "antd";
import { useState, useEffect } from "react";
import { AppCard, AppTable, AppTableActions, AppTableStatus, AppContainer } from "~/components";

const { Option } = Select;

// Interface pour les données de répartition
interface RepartitionData {
  idRepartition: number;
  codeTypePrime: string;
  idPrime: number;
  periode: string;
  annee: string;
}

// Données mockées pour la démonstration
const mockRepartitions: RepartitionData[] = [
  {
    idRepartition: 1,
    codeTypePrime: "PRIME_RENDEMENT",
    idPrime: 101,
    periode: "T1",
    annee: "2024"
  },
  {
    idRepartition: 2,
    codeTypePrime: "PRIME_PERFORMANCE",
    idPrime: 102,
    periode: "T2",
    annee: "2024"
  },
  {
    idRepartition: 3,
    codeTypePrime: "PRIME_RENDEMENT",
    idPrime: 103,
    periode: "T3",
    annee: "2024"
  },
  {
    idRepartition: 4,
    codeTypePrime: "PRIME_EXCEPTIONNELLE",
    idPrime: 104,
    periode: "T4",
    annee: "2024"
  },
  {
    idRepartition: 5,
    codeTypePrime: "PRIME_PERFORMANCE",
    idPrime: 105,
    periode: "T1",
    annee: "2023"
  }
];

// Types de primes disponibles
const typesPrimes = [
  { value: "PRIME_RENDEMENT", label: "Prime de Rendement" },
  { value: "PRIME_PERFORMANCE", label: "Prime de Performance" },
  { value: "PRIME_EXCEPTIONNELLE", label: "Prime Exceptionnelle" },
  { value: "PRIME_ANNUELLE", label: "Prime Annuelle" }
];

const RepartitionsPage = () => {
  const _load = useLoaderData();
  const [filteredData, setFilteredData] = useState<RepartitionData[]>([]);
  const [selectedTypePrime, setSelectedTypePrime] = useState<string | undefined>(undefined);
  const navigate = useNavigate();

  // Utilisation de useRequest pour récupérer les données des répartitions
  const { data: repartitionsData, loading, refresh } = useRequest(
    getRepartitionsListApi,
    {
      onSuccess: (response) => {
        console.log("Répartitions récupérées:", response);
        // Si l'API retourne les données dans response.data
        const repartitions = response?.data || response || mockRepartitions;
        setFilteredData(repartitions);
      },
      onError: (error) => {
        console.error("Erreur lors du chargement des répartitions:", error);
        // En cas d'erreur, utiliser les données mockées
        setFilteredData(mockRepartitions);
      }
    }
  );

  const handleBack = () => {
    navigate(PRIMES_PATH);
  };

  // Filtrage des données par type de prime
  useEffect(() => {
    const sourceData = repartitionsData?.data || repartitionsData || mockRepartitions;
    if (selectedTypePrime) {
      const filtered = sourceData.filter((item: RepartitionData) => item.codeTypePrime === selectedTypePrime);
      setFilteredData(filtered);
    } else {
      setFilteredData(sourceData);
    }
  }, [selectedTypePrime, repartitionsData]);

  // Définition des colonnes pour ProTable
  const columns: ProColumns<RepartitionData>[] = [
    {
      title: "ID Répartition",
      dataIndex: "idRepartition",
      key: "idRepartition",
      width: 120,
      sorter: (a, b) => a.idRepartition - b.idRepartition,
    },
    {
      title: "Type de Prime",
      dataIndex: "codeTypePrime",
      key: "codeTypePrime",
      width: 180,
      render: (_: any, record: RepartitionData) => {
        const type = typesPrimes.find(t => t.value === record.codeTypePrime);
        const colors = {
          "PRIME_RENDEMENT": "blue",
          "PRIME_PERFORMANCE": "green",
          "PRIME_EXCEPTIONNELLE": "orange",
          "PRIME_ANNUELLE": "purple"
        };
        return (
          <AppTableStatus
            status={record.codeTypePrime}
            color={colors[record.codeTypePrime as keyof typeof colors] || "default"}
            text={type?.label || record.codeTypePrime}
          />
        );
      },
      filters: typesPrimes.map(type => ({ text: type.label, value: type.value })),
      onFilter: (value, record) => record.codeTypePrime === value,
    },
    {
      title: "ID Prime",
      dataIndex: "idPrime",
      key: "idPrime",
      width: 100,
      sorter: (a, b) => a.idPrime - b.idPrime,
    },
    {
      title: "Période",
      dataIndex: "periode",
      key: "periode",
      width: 100,
      render: (_: any, record: RepartitionData) => (
        <AppTableStatus status={record.periode} color="cyan" />
      ),
    },
    {
      title: "Année",
      dataIndex: "annee",
      key: "annee",
      width: 100,
      sorter: (a, b) => a.annee.localeCompare(b.annee),
      render: (_: any, record: RepartitionData) => (
        <AppTableStatus status={record.annee} color="geekblue" />
      ),
    },
    {
      title: "Actions",
      valueType: "option",
      width: 150,
      render: (_, _record) => (
        <AppTableActions>
          <Button key="view" type="link" size="small">
            Voir
          </Button>
          <Button key="edit" type="link" size="small">
            Modifier
          </Button>
          <Button key="delete" type="link" danger size="small">
            Supprimer
          </Button>
        </AppTableActions>
      ),
    },
  ];

  return (
    <AppContainer
      title="Répartitions des Primes"
      icon={<DatabaseOutlined />}
      onBack={handleBack}
      backButtonText="Retour aux primes"
      extra={[
        <Button
          key="add-repartition"
          type="primary"
          icon={<PlusCircleOutlined />}
          onClick={() => navigate("add")}
          size="large"
        >
          Ajouter une répartition
        </Button>
      ]}
    >
      <AppCard
        // title="Répartitions des Primes"
        // icon={<DatabaseOutlined />}
        description="Gestion et consultation des répartitions de primes par type et période"
        // headerBordered
        // variant="glass"
        accentColor="#1890ff"
        // extra={
        //   <Space>
        //     <Typography.Text type="secondary">
        //       Total: {filteredData.length} répartition(s)
        //     </Typography.Text>
        //   </Space>
        // }
      >
        {/* Section de filtrage */}
        <AppCard
          title="Filtres"
          icon={<FilterOutlined />}
          size="small"
          variant="gradient"
          style={{ marginBottom: 16 }}
        >
          <Select
            placeholder="Sélectionner un type de prime"
            style={{ width: 300 }}
            allowClear
            value={selectedTypePrime}
            onChange={setSelectedTypePrime}
          >
            {typesPrimes.map(type => (
              <Option key={type.value} value={type.value}>
                {type.label}
              </Option>
            ))}
          </Select>
        </AppCard>

        {/* Tableau AppTable */}
        <AppTable<RepartitionData>
          title="Liste des Répartitions"
          // description="Consultez et gérez toutes les répartitions de primes"
          // variant="elevated"
          // accentColor="#52c41a"
          columns={columns}
          dataSource={filteredData}
          rowKey="idRepartition"
          showDefaultActions={true}
          // showResultCount={false}
          resultCountText={(total, range) =>
            `${range[0]}-${range[1]} sur ${total} répartitions`
          }
          customActions={[
            <Button
              key="filter"
              icon={<FilterOutlined />}
              onClick={() => {
                // Action de filtrage personnalisée
              }}
            >
              Filtres avancés
            </Button>
          ]}
          search={false}
          // tableViewRender={(props, dom) => {
          //   return <Card title= "ddd">{dom}</Card>;
          // }}
          toolbar={{
            search: {
              placeholder: "Rechercher par ID Répartition, ID Prime, Période, Année",
              onSearch: value => {
                alert(value);
              },
            },

            // tabs: {
            //   items: [
            //     {
            //       key: "all",
            //       label: "Toutes les répartitions",
            //     },
            //     {
            //       key: "pending",
            //       label: "En attente",
            //     },
            //     {
            //       key: "approved",
            //       label: "Approuvées",
            //     },
            //     {
            //       key: "rejected",
            //       label: "Rejetées",
            //     },
            //   ],
            // },

            // menu: {
            //   type: "tab",
            //   activeKey: "all",
            //   items: [
            //     {
            //       key: "all",
            //       label: "Toutes les répartitions",
            //     },
            //     {
            //       key: "pending",
            //       label: "En attente",
            //     },
            //     {
            //       key: "approved",
            //       label: "Approuvées",
            //     },
            //     {
            //       key: "rejected",
            //       label: "Rejetées",
            //     },
            //   ],
            // },

            actions: [
              <Button
                key="add-repartition"
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={() => navigate("add")}
                size="large"
              >
                Ajouter une répartition
              </Button>
            ],
            multipleLine: true,
            // filter: [
            //   <Select
            //     key="type-prime"
            //     placeholder="Sélectionner un type de prime"
            //     style={{ width: 300 }}
            //     allowClear
            //     value={selectedTypePrime}
            //     onChange={setSelectedTypePrime}
            //   >
            //     {typesPrimes.map(type => (
            //       <Option key={type.value} value={type.value}>
            //         {type.label}
            //       </Option>
            //     ))}
            //   </Select>,
            //   <Button
            //     key="refresh"
            //     onClick={() => {
            //       setLoading(true);
            //       setTimeout(() => setLoading(false), 1000);
            //     }}
            //   >
            //     Actualiser
            //   </Button>
            // ],
          }}
          toolBarRender={() => [
            <Button
              key="refresh"
              onClick={() => {
                refresh();
              }}
            >
              Actualiser
            </Button>
          ]}
          options={{
            setting: {
              listsHeight: 400,
            },
            reload: false,
            density: false,
            fullScreen: true,


          }}
        />
      </AppCard>
    </AppContainer>
  );
};

export default RepartitionsPage;
