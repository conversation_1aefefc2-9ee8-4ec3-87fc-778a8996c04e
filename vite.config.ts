import react from '@vitejs/plugin-react-swc'
import autoprefixer from "autoprefixer"
import tailwindcss from "tailwindcss"
import { defineConfig, loadEnv } from 'vite'
import tsconfigPaths from "vite-tsconfig-paths"

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // const env = loadEnv(mode, import.meta.dirname, '')
  const env = loadEnv(mode, './', '')

  return {
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    plugins: [react(), tsconfigPaths()],
    server: {
      proxy: {
        "/api": {
          target: env.VITE_URL_API,
          changeOrigin: true,
          secure: env.VITE_URL_API.startsWith("https://") ? true : false,
          rewrite: path => path.replace(/^\/api/, ""),
        },
        "/ipa": {
          target: env.VITE_URL_API2,
          changeOrigin: true,
          secure: env.VITE_URL_API2.startsWith("https://") ? true : false,
          rewrite: path => path.replace(/^\/ipa/, ""),
        },
      },
    },
  }
})
