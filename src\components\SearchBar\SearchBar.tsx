import React from 'react';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { createStyles } from '~/themes';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Rechercher un service...",
  className
}) => {
  const { styles } = useStyles();

  return (
    <div className={`${styles.searchContainer} ${className || ''}`}>
      <Input
        size="large"
        placeholder={placeholder}
        prefix={<SearchOutlined className={styles.searchIcon} />}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={styles.searchInput}
        allowClear
      />
    </div>
  );
};

const useStyles = createStyles(({ token }) => ({
  searchContainer: {
    width: '100%',
    maxWidth: '600px',
    margin: '0 auto',
    marginBottom: '2rem',
  },
  searchInput: {
    borderRadius: '50px',
    border: `2px solid ${token.colorPrimary}`,
    fontSize: '16px',
    padding: '12px 20px',
    boxShadow: `0 4px 12px rgba(0, 0, 0, 0.1)`,
    '&:hover': {
      borderColor: token.colorPrimaryHover,
      boxShadow: `0 6px 16px rgba(0, 0, 0, 0.15)`,
    },
    '&:focus': {
      borderColor: token.colorPrimaryActive,
      boxShadow: `0 0 0 2px ${token.colorPrimary}20`,
    },
    '& .ant-input': {
      fontSize: '16px',
      padding: '0',
    }
  },
  searchIcon: {
    fontSize: '18px',
    color: token.colorPrimary,
  }
}));

export default SearchBar;
