import { ProFormDatePicker, ProFormDigit, ProFormGroup, ProFormSelect, ProFormText } from "@ant-design/pro-components"

function SituationFinanciere() {
  return (
    <ProFormGroup
      style={{
        gap: "0 32px",
      }}
    >
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "comSuivEscort"]}
        label="Comité Suivi Escorte"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "codeCorps"]}
        label="Corps"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "gradePaye"]}
        label="Grade payé"
        colProps={{
          span: 8,
        }}
      />
      <ProFormDigit
        // width="xs"
        name={["situationFnc", "coefPaye"]}
        label="Coef. payé"
        colProps={{
          span: 6,
        }}
      />
      <ProFormDigit
        // width="xs"
        name={["situationFnc", "coefReel"]}
        label="Coef. réel"
        colProps={{
          span: 6,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "gradeReel"]}
        label="Grade réel"
        colProps={{
          span: 6,
        }}
      />
      <ProFormDatePicker
        // width="sm"
        name={["situationFnc", "dateEffet"]}
        label="Date effet"
        colProps={{
          span: 6,
        }}
      />

      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "fonction"]}
        label="Fonction"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "primeForfait"]}
        label="Prime Forfait"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "qualPrime"]}
        label="Qual. Prime"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationFnc", "numeroSecuSocial"]}
        label="N° Sécurité Sociale"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationFnc", "pointPaye"]}
        label="Point Payé"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationFnc", "rib"]}
        label="RIB(Compte Bancaire)"
        colProps={{
          span: 8,
        }}
      />
    </ProFormGroup>
  )
}

export default SituationFinanciere
