import { <PERSON><PERSON>ontainer, ProColumns, ProTable } from "@ant-design/pro-components"
import { Space, Tag } from "antd"
import { useEffect, useState } from "react"
import request from "umi-request"

const waitTimePromise = async (time: number = 100) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

const waitTime = async (time: number = 100) => {
  await waitTimePromise(time)
}

type GithubIssueItem = {
  url: string
  id: number
  number: number
  title: string
  labels: {
    name: string
    color: string
  }[]
  state: string
  comments: number
  created_at: string
  updated_at: string
  closed_at?: string
}

const columns: ProColumns<GithubIssueItem>[] = [
  {
    dataIndex: "index",
    valueType: "indexBorder",
  },
  {
    title: "Titre",
    dataIndex: "title",
    copyable: true,
    ellipsis: true,
    formItemProps: {
      rules: [
        {
          required: true,
          message: "Titre des ...",
        },
      ],
    },
  },
  {
    disable: true,
    title: "State",
    dataIndex: "state",
    filters: true,
    onFilter: true,
    ellipsis: true,
    valueType: "select",
    valueEnum: {
      all: { text: "all".repeat(5) },
      open: {
        text: "Error",
        status: "Error",
      },
      closed: {
        text: "Success",
        status: "Success",
        disabled: true,
      },
      processing: {
        text: "Processing",
        status: "Processing",
      },
    },
  },
  {
    disable: true,
    title: "Labels",
    dataIndex: "labels",
    search: false,
    renderFormItem: (_, { defaultRender }) => {
      return defaultRender(_)
    },
    render: (_, record) => (
      <Space>
        {record.labels.map(({ name, color }) => (
          <Tag color={color} key={name}>
            {name}
          </Tag>
        ))}
      </Space>
    ),
  },
]

function ConvoyageAdminPage() {
  const [data, setData] = useState<GithubIssueItem[]>()

  useEffect(() => {
    ;(async () => {
      const data = await request<{
        data: GithubIssueItem[]
      }>("https://proapi.azurewebsites.net/github/issues")
      setData(data.data)
    })()
  }, [])

  return (
    <PageContainer title="Liste des convoyages">
      <ProTable
        columns={columns}
        // actionRef={actionRef}
        cardBordered
        dataSource={data}
        // request={async (params, sort, filter) => {
        //   console.log(sort, filter)
        //   await waitTime(2000)
        //   return request<{
        //     data: GithubIssueItem[]
        //   }>("https://proapi.azurewebsites.net/github/issues", {
        //     params,
        //   })
        // }}
        editable={{
          type: "multiple",
        }}
        // columnsState={{
        //   persistenceKey: "pro-table-singe-demos",
        //   persistenceType: "localStorage",
        //   defaultValue: {
        //     option: { fixed: "right", disable: true },
        //   },
        //   onChange(value) {
        //     console.log("value: ", value)
        //   },
        // }}
        rowKey="id"
        // search={{
        //   labelWidth: "auto",
        // }}
        // options={{
        //   setting: {
        //     listsHeight: 400,
        //   },
        // }}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === "get") {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        pagination={{
          pageSize: 5,
          onChange: page => console.log(page),
        }}
        dateFormatter="string"
        headerTitle="IMANDINE"
        // toolBarRender={() => [
        //   <Button
        //     key="button"
        //     icon={<PlusOutlined />}
        //     onClick={() => {
        //       actionRef.current?.reload()
        //     }}
        //     type="primary"
        //   >
        //     新建
        //   </Button>,
        //   <Dropdown
        //     key="menu"
        //     menu={{
        //       items: [
        //         {
        //           label: "1st item",
        //           key: "1",
        //         },
        //         {
        //           label: "2nd item",
        //           key: "2",
        //         },
        //         {
        //           label: "3rd item",
        //           key: "3",
        //         },
        //       ],
        //     }}
        //   >
        //     <Button>
        //       <EllipsisOutlined />
        //     </Button>
        //   </Dropdown>,
        // ]}
      />
    </PageContainer>
  )
}

export default ConvoyageAdminPage
