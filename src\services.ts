import { getUUID } from "./services/utils";

export interface Service {
  id: string;
  title: string;
  description: string;
  icon?: string;
  imageUrl?: string;
  route: string;
  category: 'public' | 'private';
  requiredRoles?: string[];
  isExternal?: boolean;
  keywords?: string[];
}

const services: Service[] = [
  {
    id: getUUID('convoyage'),
    title: 'Fiche de Convoyage Des Produits Agricoles',
    description: 'Mesures de suivi des mouvements des chargements des produits agricoles.',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/18/Benin_Customs_Logo.svg/800px-Benin_Customs_Logo.svg.png',
    route: '/admin/convoyage',
    category: 'private',
    requiredRoles: ['agent'],
    keywords: ['convoyage', 'produits', 'agricoles', 'transport', 'suivi']
  },
  {
    id: getUUID('customs-webb'),
    title: 'Customs Webb',
    description: 'Plateforme de dédouanement électronique du Bénin.',
    // imageUrl: 'https://cw.douanes.bj/favicon.ico',
    route: 'https://cw.douanes.bj/',
    category: 'public',
    isExternal: true,
    keywords: ['customs', 'webb', 'dédouanement', 'électronique', 'déclaration']
  },
  {
    id: getUUID('guce'),
    title: 'Guce Bénin',
    description: 'Guichet Unique du Commerce Extérieur du Bénin.',
    imageUrl: 'https://guce.gouv.bj/favicon.ico',
    route: 'https://guce.gouv.bj/',
    category: 'public',
    isExternal: true,
    keywords: ['guce', 'commerce', 'extérieur', 'guichet', 'unique']
  }
];


// LES METHODES DE SERVICES
export const searchServices = (query: string, userServices: Service[]): Service[] => {
  if (!query.trim()) return userServices;

  const searchTerm = query.toLowerCase().trim();

  return userServices.filter(service =>
    service.title.toLowerCase().includes(searchTerm) ||
    service.description.toLowerCase().includes(searchTerm) ||
    service.keywords?.some(keyword => keyword.toLowerCase().includes(searchTerm))
  );
};

export const getAvailableServices = (isAuthenticated: boolean, userRole: string = ''): Service[] => {
  if (!isAuthenticated) {
    return getPublicServices();
  }

  // Si l'utilisateur est connecté, on retourne tous les services publics + les services privés auxquels il a accès
  const publicServices = getPublicServices();
  const privateServices = !userRole? [] : getPrivateServices().filter(service => {
    if (userRole.toLowerCase() === 'admin' || !service.requiredRoles || service.requiredRoles.length === 0) return true;
    return service.requiredRoles.includes(userRole.toLowerCase());
  });

  return [...publicServices, ...privateServices];
};

const getPublicServices = (): Service[] => {
  return getServicesByCategory('public');
};

const getPrivateServices = (): Service[] => {
  return getServicesByCategory('private');
};

const getServicesByCategory = (category: Service['category']): Service[] => {
  return services.filter(service => service.category === category);
};
