import { Tabs, TabsProps } from "antd"
import { cloneElement, useCallback, useEffect, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router"
import { addTag, removeTagByPath, setActiveTagByPath } from "~/redux/slices/tagSlice"
import { useAppLocal } from "~/hooks"
import { RootState } from "~/redux/store"
import TagsViewAction from "./TagViewAction"
import { closestCenter, DndContext, PointerSensor, useSensor } from "@dnd-kit/core"
import { arrayMove, horizontalListSortingStrategy, SortableContext, useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { PATH_ADMIN } from "~/constants"
import { Menus } from "~/models"

const DraggableTabNode = ({ ...props }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props["data-node-key"],
  })
  const style = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    // cursor: "move",
  }
  return cloneElement(props.children, {
    ref: setNodeRef,
    style,
    ...attributes,
    ...listeners,
  })
}

type TagsViewProps = Readonly<TabsProps>

function TagsView({...others }: TagsViewProps) {
  const { local } = useAppLocal()
  const { pathname } = useLocation()
  const { menus, tags, activeTagId } = useSelector((state: RootState) => state.tag)
  const [items, setItems] = useState(
    tags.map((tag: any) => {
      const isHomeTag = tag.path === PATH_ADMIN.root
      return {
        key: tag.path,
        icon: tag.icon,
        label: isHomeTag ? undefined : tag.labels[local as keyof typeof tag.labels], // Pas de label pour le tag home
        closable: !isHomeTag, // Le tag home n'est pas supprimable
      }
    })
  )
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const nodesRef = useRef({ dispatch, navigate })

  const sensor = useSensor(PointerSensor, {
    activationConstraint: {
      distance: 10,
    },
  })
  const onDragEnd = useCallback(({ active, over }: any) => {
    if (active.id !== over?.id) {
      setItems(prev => {
        const activeIndex = prev.findIndex(i => i.key === active.id)
        const overIndex = prev.findIndex(i => i.key === over?.id)
        return arrayMove(prev, activeIndex, overIndex)
      })
    }
  }, [])

  // onClick tag
  const onChange = useCallback(
    (key: string) => {
      const ids = key || pathname
      const tag = tags.find(item => item.path === ids)
      if (tag?.path && tag.path !== activeTagId) {
        nodesRef.current.dispatch(setActiveTagByPath(tag.path))
        nodesRef.current.navigate(tag.path)
      }
    },
    [pathname, activeTagId, tags]
  )

  // onRemove tag
  const onClose = useCallback(async (targetKey: any) => {
    // Empêcher la suppression du tag home
    if (targetKey === PATH_ADMIN.root) {
      return
    }
    nodesRef.current.dispatch(removeTagByPath(targetKey))
  }, [])

  useEffect(() => {
    if (!tags.find((t: Menus) => t.path === pathname)) {
      // Fonction pour remonter dans la hiérarchie des chemins jusqu'à trouver un menu correspondant
      const findParentMenu = (currentPath: string): Menus | undefined => {
        // Essayer de trouver un menu avec le chemin actuel
        const pageMenu = menus.find((m: Menus) => m.path === currentPath)

        if (pageMenu) {
          return pageMenu
        }

        // Si pas trouvé et qu'on n'est pas à la racine, remonter au parent
        if (currentPath !== '/' && currentPath !== '') {
          // Supprimer le dernier segment du chemin
          const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/')) || '/'
          return findParentMenu(parentPath)
        }

        return undefined
      }

      const pageMenu = findParentMenu(pathname)
      if (pageMenu) {
        nodesRef.current.dispatch(addTag(pageMenu))
        nodesRef.current.dispatch(setActiveTagByPath(pageMenu.path))
      }
    }
  }, [pathname, menus, tags])

  useEffect(() => {
    setItems(
      tags.map((tag)=> {
        const isHomeTag = tag.path === PATH_ADMIN.root
        return {
          key: tag.path,
          icon: tag.icon,
          label: isHomeTag ? undefined : tag.labels[local as keyof typeof tag.labels], // Pas de label pour le tag home
          closable: !isHomeTag, // Le tag home n'est pas supprimable
        }
      })
    )
  }, [local, tags])

  return (
    <Tabs
      tabBarStyle={{ margin: 0 }}
      onChange={onChange}
      activeKey={activeTagId}
      type="editable-card"
      tabPosition="bottom"
      hideAdd
      animated
      draggable
      onEdit={(targetKey, action) => action === "remove" && onClose(targetKey)}
      tabBarExtraContent={<TagsViewAction />}
      items={items}
      renderTabBar={(tabBarProps, DefaultTabBar) => (
        <DndContext sensors={[sensor]} onDragEnd={onDragEnd} collisionDetection={closestCenter}>
          <SortableContext items={items.map(i => i.key)} strategy={horizontalListSortingStrategy}>
            <DefaultTabBar {...tabBarProps}>
              {node => (
                <DraggableTabNode {...node.props} key={node.key}>
                  {node}
                </DraggableTabNode>
              )}
            </DefaultTabBar>
          </SortableContext>
        </DndContext>
      )}
      {...others}
    />
  )
}

export default TagsView
