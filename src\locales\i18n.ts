import i18n from "i18next"
import { initReactI18next } from "react-i18next"
import translationEN from "./en/translation.json"
import translationFR from "./fr/translation.json"

const resources = {
  en: {
    translation: translationEN,
  },
  fr: {
    translation: translationFR,
  },
}

i18n.use(initReactI18next) // Intègre i18next à React
.init({
  resources,
  lng: "fr",
  fallbackLng: 'fr',
  interpolation: {
    escapeValue: false,
  },
})

export default i18n
