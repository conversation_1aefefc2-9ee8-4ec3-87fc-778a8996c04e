# Build step
FROM node:latest AS build

# Installer pnpm globalement
RUN npm install -g pnpm

WORKDIR /app

# Copier les fichiers de configuration pnpm et package.json
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Installer les dépendances avec pnpm
RUN pnpm install --frozen-lockfile

# Copier le reste du code source
COPY . .

# Construire l'application
RUN pnpm run build

# Serveur static
FROM nginx:latest

# Copier les fichiers buildés
COPY --from=build /app/dist /usr/share/nginx/html

# Copier la configuration nginx personnalisée
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Supprimer la configuration par défaut de nginx
RUN rm -f /etc/nginx/conf.d/default.conf.bak

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
