import { useEffect, useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import {
  Button,
  Typography,
  Table,
  Space,
  message,
  Modal,
  Form,
  InputNumber,
  Select,
  Card,
  Tag,
  Spin
} from "antd";
import { ArrowLeftOutlined, EditOutlined, SaveOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router";
import { Prime, PrimeType } from "~/models/Prime";
import { Fonction, CoefficientConfig } from "~/models/Agent";
import { getPrimeById } from "~/services/api/primeService";
import { getFonctions, getCoefficientByFonctionAndPrime, updateCoefficient } from "~/services/api/fonctionService";

const { Option } = Select;

interface CoefficientData {
  id: string;
  fonction: Fonction;
  coefficient: number;
  isModified?: boolean;
}

const CoefficientManagementPage = () => {
  const { primeId } = useParams<{ primeId: string }>();
  const [prime, setPrime] = useState<Prime | null>(null);
  const [fonctions, setFonctions] = useState<Fonction[]>([]);
  const [coefficients, setCoefficients] = useState<CoefficientData[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCoefficient, setEditingCoefficient] = useState<CoefficientData | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const fetchData = async () => {
    if (!primeId) return;

    setLoading(true);
    try {
      // Charger la prime
      const primeData = await getPrimeById(primeId);
      setPrime(primeData);

      // Charger les fonctions
      const fonctionsData = []// await getFonctions();
      setFonctions(fonctionsData);

      // Charger les coefficients pour chaque fonction
      const coefficientsData: CoefficientData[] = [];
      for (const fonction of fonctionsData) {
        const coefficient = await getCoefficientByFonctionAndPrime(fonction.code, primeData.type);
        coefficientsData.push({
          id: `${fonction.code}_${primeData.type}`,
          fonction,
          coefficient: coefficient || 1.0,
        });
      }
      setCoefficients(coefficientsData);
    } catch (error) {
      message.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [primeId]);

  const handleBack = () => {
    navigate("/admin/primes/coefficients");
  };

  const handleEdit = (coefficient: CoefficientData) => {
    setEditingCoefficient(coefficient);
    form.setFieldsValue({
      coefficient: coefficient.coefficient,
    });
    setIsModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const newCoefficient = values.coefficient;

      // Mettre à jour localement
      setCoefficients(prev =>
        prev.map(coef =>
          coef.id === editingCoefficient?.id
            ? { ...coef, coefficient: newCoefficient, isModified: true }
            : coef
        )
      );

      setIsModalVisible(false);
      message.success("Coefficient modifié localement. N'oubliez pas de sauvegarder.");
    } catch (error) {
      message.error("Erreur lors de la modification du coefficient");
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSaveAll = async () => {
    setSaving(true);
    try {
      const modifiedCoefficients = coefficients.filter(coef => coef.isModified);

      for (const coef of modifiedCoefficients) {
        await updateCoefficient(coef.fonction.code, prime!.type, coef.coefficient);
      }

      // Marquer comme sauvegardé
      setCoefficients(prev =>
        prev.map(coef => ({ ...coef, isModified: false }))
      );

      message.success("Tous les coefficients ont été sauvegardés avec succès");
    } catch (error) {
      message.error("Erreur lors de la sauvegarde des coefficients");
    } finally {
      setSaving(false);
    }
  };

  const getPrimeTypeLabel = (type: PrimeType) => {
    const labels = {
      [PrimeType.ANNUAL]: "Annuelle",
      [PrimeType.PERFORMANCE]: "Performance",
      [PrimeType.PROJECT]: "Projet",
      [PrimeType.EXCEPTIONAL]: "Exceptionnelle",
      [PrimeType.OTHER]: "Autre",
    };
    return labels[type] || type;
  };

  const getPrimeTypeColor = (type: PrimeType) => {
    const colors = {
      [PrimeType.ANNUAL]: "blue",
      [PrimeType.PERFORMANCE]: "green",
      [PrimeType.PROJECT]: "orange",
      [PrimeType.EXCEPTIONAL]: "purple",
      [PrimeType.OTHER]: "default",
    };
    return colors[type] || "default";
  };

  const columns = [
    {
      title: "Fonction",
      dataIndex: ["fonction", "libelle"],
      key: "fonction",
      render: (libelle: string) => (
        <Typography.Text strong>{libelle}</Typography.Text>
      ),
    },
    {
      title: "Code Fonction",
      dataIndex: ["fonction", "code"],
      key: "code",
      render: (code: string) => (
        <Typography.Text code>{code}</Typography.Text>
      ),
    },
    {
      title: "Coefficient",
      dataIndex: "coefficient",
      key: "coefficient",
      render: (coefficient: number, record: CoefficientData) => (
        <Space>
          <Typography.Text
            strong={record.isModified}
            style={{ color: record.isModified ? '#1890ff' : undefined }}
          >
            {coefficient.toFixed(2)}
          </Typography.Text>
          {record.isModified && (
            <Tag color="blue">Modifié</Tag>
          )}
        </Space>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: CoefficientData) => (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          Modifier
        </Button>
      ),
    },
  ];

  const hasModifications = coefficients.some(coef => coef.isModified);

  return (
    <PageContainer
      header={{
        title: (
          <Space>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
            />
            <Typography.Title level={3} style={{ margin: 0 }}>
              Gestion des Coefficients
            </Typography.Title>
          </Space>
        ),
        extra: hasModifications ? (
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveAll}
            loading={saving}
          >
            Sauvegarder les modifications
          </Button>
        ) : null
      }}
    >
      <Spin spinning={loading}>
        {prime && (
          <Card style={{ marginBottom: 24 }}>
            <div>
              <Typography.Title level={4} style={{ margin: 0, marginBottom: 8 }}>
                {prime.name}
              </Typography.Title>
              <Space>
                <Tag color={getPrimeTypeColor(prime.type)}>
                  {getPrimeTypeLabel(prime.type)}
                </Tag>
                <Typography.Text strong>
                  {prime.amount.toLocaleString()} €
                </Typography.Text>
                <Typography.Text type="secondary">
                  {new Date(prime.date).toLocaleDateString()}
                </Typography.Text>
              </Space>
              {prime.description && (
                <Typography.Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
                  {prime.description}
                </Typography.Paragraph>
              )}
            </div>
          </Card>
        )}

        <Table
          dataSource={coefficients}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 20 }}
          title={() => (
            <Typography.Title level={5} style={{ margin: 0 }}>
              Coefficients par Fonction ({coefficients.length} fonctions)
            </Typography.Title>
          )}
        />
      </Spin>

      <Modal
        title="Modifier le coefficient"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="Modifier"
        cancelText="Annuler"
      >
        {editingCoefficient && (
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Fonction: </Typography.Text>
            <Typography.Text>{editingCoefficient.fonction.libelle}</Typography.Text>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="coefficient"
            label="Coefficient"
            rules={[
              { required: true, message: "Veuillez saisir le coefficient" },
              { type: 'number', min: 0, message: "Le coefficient doit être positif" }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              step={0.1}
              precision={2}
              placeholder="Coefficient"
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default CoefficientManagementPage;
