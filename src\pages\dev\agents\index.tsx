import { PageContainer } from "@ant-design/pro-components"
import { useRequest } from "ahooks"
import { Skeleton, Typography } from "antd"
// import { apiAgents } from "app/apis/agents.api"
import TableAgents from "./TableAgents"
import request from "~/services/request"

const apiAgents = () => request("/agents")

const Agents = () => {
  const { data, loading } = useRequest(apiAgents)

  return (
    <PageContainer
      header={{
        title: <Typography.Title level={3}>Agents</Typography.Title>,
        // ghost: false,
      }}
    >
      <Skeleton loading={loading} active>
        <TableAgents dataSource={data?.data} />
      </Skeleton>
    </PageContainer>
  )
}

export default Agents
