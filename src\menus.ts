import {
  BranchesOutlined,
  IdcardOutlined,
  InfoCircleOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BookOutlined,
  DashboardOutlined,
  AppstoreAddOutlined,
  BugOutlined,
  GithubOutlined,
  ProductOutlined,
  SecurityScanOutlined,
  SnippetsOutlined,
  UserOutlined,
  HomeOutlined,
} from "@ant-design/icons"
import React from "react"
import { HOME, PATH_ADMIN } from "./constants"
import { Menus } from "./models"
import { getUUID, createPath } from "./services/utils"
import { PRIMES_PATH } from "./pages/admin/dafl/primes"


const getItem = (
  // utiliser && dans le key pour séparer les roles et un tableau pour définir les permissions
  // exemples: rol1&&rol2&&rol3
  // exemples: [per1, per2, per3]
  key: 'home' | 'group' | string | string[],
  names: string[],
  path?: string | null,
  iconComponent?: any,
  children?: Menus[],
): Menus => {
  const isHome = key === 'home'
  const isGroup = key === 'group'
  const isString = typeof key === "string"
  iconComponent = isHome ? HomeOutlined : iconComponent
  return {
    key: getUUID(path),
    icon: iconComponent? React.createElement(iconComponent, null): undefined,
    labels: { fr: names?.[0], en: names?.[1] ?? names?.[0] },
    path: isHome ? PATH_ADMIN.root : !isGroup ? path : path ?? "#",
    hideInMenu: isHome ? true : undefined,
    roles: isString && !isHome && !isGroup ? key.trim().split("&&") : undefined,
    permissions: !isString ? key: undefined,
    disabled: isGroup,
    disabledTooltip: isGroup,
    children,
  } as Menus
}

export const menuItems = [
  getItem("home", ["🏦", "🏦"]),
  getItem("*", ["Tableaux de bord", "Dashboards"], null, PieChartOutlined, [
    getItem([], ["Custums webb"],),
    getItem([], ["Sydonia world"]),
    getItem([], ["Sydonia ++"]),
  ]),

  getItem("group", ["Services"]),
  getItem("dafl", ["Service du DAFL", "DAFL Service"], PATH_ADMIN.dafl, ProductOutlined, [
    getItem(["*"], ["Gestion des Primes", "Primes Management"], PRIMES_PATH, IdcardOutlined),
  ]),

  getItem("group", ["Pages"]),
  getItem("agents", ["Agents"], PATH_ADMIN.agents, IdcardOutlined),
  getItem("convoyages", ["Convoyage"], PATH_ADMIN.convoyage, IdcardOutlined),
  getItem("utilisateurs", ["Utilisateurs"], PATH_ADMIN.utilisateurs, IdcardOutlined),

]
