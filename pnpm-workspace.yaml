ignoredBuiltDependencies:
  - '@swc/core'
  - esbuild
  - 'typescript'
  - '@types/*'

# Configuration du cache
cache:
  # Dossier de cache personnalisé
  store: '.pnpm-store'
  # Durée de conservation du cache (en jours)
  ttl: 7

# Configuration des scripts
scripts:
  # Scripts globaux pour tous les packages
  dev: 'pnpm -r dev'
  build: 'pnpm -r build'
  test: 'pnpm -r test'
  lint: 'pnpm -r lint'
  format: 'pnpm -r format'

# # Configuration des hooks
# hooks:
#   # Exécuter les tests avant chaque commit
#   precommit: 'npm run test'
#   # Vérifier les types avant chaque build
#   prebuild: 'npm run type-check'