import { useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import {
  Button,
  Typography,
  Table,
  Space,
  Tag,
  Modal,
  message,
  Form,
  Input,
  Select,
  Switch
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import { TypePrime } from "~/models/Prime";
import { useNavigate, useLoaderData } from "react-router";
import { addTypesPrimesApi, deleteTypePrimeApi, updateTypePrimeApi } from "~/services/api/primeService";
import AppError from "~/services/AppError";

const { Option } = Select;

type TypePrimeFormData = TypePrime



const TypePrimeManagementPage = () => {
  const typesPrimes = useLoaderData() as TypePrime[];
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTypePrime, setEditingTypePrime] = useState<TypePrime | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate("/admin/primes");
  };

  const handleAdd = () => {
    setEditingTypePrime(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (typePrime: TypePrime) => {
    setEditingTypePrime(typePrime);
    form.setFieldsValue({
      codeTypePrime: typePrime.codeTypePrime,
      libelle: typePrime.libelle,
      description: typePrime.description,
      periodicite: typePrime.periodicite,
      nom_table_prime: typePrime.nom_table_prime,
      avecUnite: typePrime.avecUnite
    });
    setIsModalVisible(true);
  };

  const handleDelete = (codeTypePrime: string) => {
    Modal.confirm({
      title: "Êtes-vous sûr de vouloir supprimer ce type de prime ?",
      content: "Cette action est irréversible.",
      okText: "Oui",
      okType: "danger",
      cancelText: "Non",
      onOk: async () => {
        try {
          // TODO: Implémenter la suppression d'un type de prime
          await deleteTypePrimeApi(codeTypePrime);
          message.success("Type de prime supprimé avec succès");
          // Recharger la page pour actualiser les données
          window.location.reload();
        } catch (error) {
          AppError.message(error, "Erreur lors de la suppression du type de prime");
        }
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const formData = {
        code_type_prime: values.codeTypePrime,
        libelle: values.libelle,
        description: values.description,
        periodicite: values.periodicite,
        nom_table_prime: values.nom_table_prime,
        avecUnite: values.avecUnite
      };

      if (editingTypePrime) {
        // TODO: Implémenter la modification d'un type de prime
        try {
          await updateTypePrimeApi(editingTypePrime.codeTypePrime, formData)
          message.success("Type de prime modifié avec succès");
        } catch (error) {
          AppError.throw(error)
        }
      } else {
        // TODO: Implémenter la création d'un type de prime
        try {
          await addTypesPrimesApi(formData)
          message.success("Type de prime créé avec succès");
        } catch (error) {
          AppError.throw(error)
        }
      }

      setIsModalVisible(false);
      // Recharger la page pour actualiser les données
      window.location.reload();
    } catch (error) {
      AppError.message(error, "Erreur lors de l'enregistrement du type de prime");
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const getPeriodiciteLabel = (periodicite: string) => {
    const labels: { [key: string]: string } = {
      'annuelle': 'Annuelle',
      'trimestrielle': 'Trimestrielle',
      'mensuelle': 'Mensuelle'
    };
    return labels[periodicite] || periodicite;
  };

  const columns = [
    {
      title: "Code",
      dataIndex: "codeTypePrime",
      key: "codeTypePrime",
    },
    {
      title: "Libellé",
      dataIndex: "libelle",
      key: "libelle",
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Périodicité",
      dataIndex: "periodicite",
      key: "periodicite",
      render: (periodicite: string) => getPeriodiciteLabel(periodicite),
    },
    {
      title: "Table Prime",
      dataIndex: "nom_table_prime",
      key: "nom_table_prime",
    },
    {
      title: "Avec Unité",
      dataIndex: "avecUnite",
      key: "avecUnite",
      render: (avecUnite: boolean) => (
        <Tag color={avecUnite ? "success" : "default"}>
          {avecUnite ? "Oui" : "Non"}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: TypePrime) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Modifier
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.codeTypePrime)}
          >
            Supprimer
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: (
          <Space>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
            />
            <Typography.Title level={3} style={{ margin: 0 }}>
              Gestion des Types de Primes
            </Typography.Title>
          </Space>
        ),
        extra: (
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            Ajouter un type de prime
          </Button>
        )
      }}
    >
      <Table
        dataSource={typesPrimes}
        columns={columns}
        rowKey="codeTypePrime"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} types de primes`,
        }}
      />

      <Modal
        title={editingTypePrime ? "Modifier le type de prime" : "Ajouter un type de prime"}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        okText="Enregistrer"
        cancelText="Annuler"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            avecUnite: false,
          }}
        >
          <Form.Item
            name="codeTypePrime"
            label="Code du type de prime"
            rules={[{ required: true, message: "Veuillez saisir le code du type de prime" }]}
          >
            <Input placeholder="Code du type de prime (ex: TSD)" />
          </Form.Item>

          <Form.Item
            name="libelle"
            label="Libellé"
            rules={[{ required: true, message: "Veuillez saisir le libellé" }]}
          >
            <Input placeholder="Libellé du type de prime" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: "Veuillez saisir la description" }]}
          >
            <Input.TextArea rows={3} placeholder="Description du type de prime" />
          </Form.Item>

          <Form.Item
            name="periodicite"
            label="Périodicité"
            rules={[{ required: true, message: "Veuillez sélectionner la périodicité" }]}
          >
            <Select placeholder="Sélectionner la périodicité">
              <Option value="annuelle">Annuelle</Option>
              <Option value="trimestrielle">Trimestrielle</Option>
              <Option value="mensuelle">Mensuelle</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="nom_table_prime"
            label="Nom de la table prime"
            rules={[{ required: true, message: "Veuillez saisir le nom de la table prime" }]}
          >
            <Input placeholder="Nom de la table prime (ex: primetsd)" />
          </Form.Item>

          <Form.Item
            name="avecUnite"
            label="Avec unité de versement"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default TypePrimeManagementPage;
