# Politique de Sécurité

## Versions Supportées

Nous nous engageons à maintenir la sécurité des versions suivantes :

| Version | Supportée          | Fin de Support |
| ------- | ------------------ | -------------- |
| 1.x.x   | :white_check_mark: | Décembre 2024  |
| 0.x.x   | :x:                | Décembre 2023  |

## Signaler une Vulnérabilité

Nous prenons la sécurité de notre projet très au sérieux. Si vous découvrez une vulnérabilité, merci de :

1. **Ne pas divulguer publiquement** la vulnérabilité
2. Envoyer un email à [<EMAIL>] avec :
   - Description détaillée de la vulnérabilité
   - Étapes pour reproduire le problème
   - Impact potentiel
   - Suggestions de correction (si possible)

### Processus de Gestion

1. **Réponse Initiale** : Nous répondrons dans les 48 heures
2. **Évaluation** : Nous évaluerons la vulnérabilité dans les 7 jours
3. **Mise à jour** : Vous recevrez des mises à jour régulières sur le statut
4. **Correction** : Une fois la vulnérabilité confirmée, nous :
   - Développerons un correctif
   - Testerons la solution
   - Déploierons la mise à jour
   - Vous informerons de la résolution

### Bonnes Pratiques de Sécurité

Pour contribuer à la sécurité du projet :

1. Maintenez vos dépendances à jour
2. Utilisez des outils d'analyse de sécurité
3. Suivez les bonnes pratiques de développement sécurisé
4. Signalez immédiatement toute vulnérabilité découverte

## Mises à Jour de Sécurité

Les mises à jour de sécurité seront :
- Publiées via les releases GitHub
- Documentées dans le changelog
- Communiquées aux utilisateurs via les canaux appropriés
